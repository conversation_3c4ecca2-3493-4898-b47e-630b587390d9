{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/vlsi-workflow/client/src/components/WorkflowEditor.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useCallback, useRef, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport ReactFlow, { Background, Controls, MiniMap, useReactFlow, ReactFlowProvider } from 'reactflow';\nimport 'reactflow/dist/style.css';\nimport toast from 'react-hot-toast';\nimport { nodeTypes } from './nodes';\nimport { edgeTypes } from './edges';\nimport Sidebar from './sidebar/Sidebar';\nimport Toolbar from './Toolbar';\nimport ContextMenu from './ContextMenu';\nimport { useTheme } from '../contexts/ThemeContext';\nimport ConfigModal from './ConfigModal';\nimport { useWorkflow } from '../contexts/WorkflowContext';\nimport { useSocket } from '../contexts/SocketContext';\nimport { workflowAPI } from '../services/api';\nimport { createNewBranch, duplicateNode, validateBranchCreation } from '../utils/workflowBranching';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WorkflowEditorContent = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const reactFlowWrapper = useRef(null);\n  const {\n    project\n  } = useReactFlow();\n  const {\n    getCurrentTheme\n  } = useTheme();\n  const {\n    nodes,\n    edges,\n    onNodesChange,\n    onEdgesChange,\n    onConnect,\n    setWorkflow,\n    addNode,\n    addEdge,\n    updateNode,\n    updateEdge,\n    setLoading,\n    setError,\n    isLoading\n  } = useWorkflow();\n  const {\n    joinWorkflow,\n    leaveWorkflow,\n    emitNodeUpdate,\n    emitEdgeUpdate,\n    socket\n  } = useSocket();\n  const [selectedNodes, setSelectedNodes] = useState([]);\n  const [selectedEdges, setSelectedEdges] = useState([]);\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  const [reactFlowInstance, setReactFlowInstance] = useState(null);\n\n  // Context menu state\n  const [contextMenu, setContextMenu] = useState({\n    isVisible: false,\n    position: {\n      x: 0,\n      y: 0\n    },\n    node: null\n  });\n\n  // Config modal state\n  const [configModal, setConfigModal] = useState({\n    isOpen: false,\n    node: null\n  });\n\n  // Load workflow on mount\n  useEffect(() => {\n    if (id) {\n      loadWorkflow(id);\n      joinWorkflow(id);\n    } else {\n      // Create new workflow\n      createNewWorkflow();\n    }\n    return () => {\n      if (id) {\n        leaveWorkflow(id);\n      }\n    };\n  }, [id]);\n\n  // Socket event listeners\n  useEffect(() => {\n    if (!socket) return;\n    const handleNodeUpdate = data => {\n      updateNode(data.nodeId, data.updates);\n    };\n    const handleEdgeUpdate = data => {\n      updateEdge(data.edgeId, data.updates);\n    };\n    const handleWorkflowUpdated = workflow => {\n      setWorkflow(workflow);\n      toast.success('Workflow updated by collaborator');\n    };\n    socket.on('nodeUpdate', handleNodeUpdate);\n    socket.on('edgeUpdate', handleEdgeUpdate);\n    socket.on('workflowUpdated', handleWorkflowUpdated);\n    return () => {\n      socket.off('nodeUpdate', handleNodeUpdate);\n      socket.off('edgeUpdate', handleEdgeUpdate);\n      socket.off('workflowUpdated', handleWorkflowUpdated);\n    };\n  }, [socket, updateNode, updateEdge, setWorkflow]);\n  const loadWorkflow = async workflowId => {\n    try {\n      setLoading(true);\n      const workflow = await workflowAPI.getWorkflow(workflowId);\n      console.log('🔄 Loaded workflow:', workflow);\n      console.log('🔄 Workflow nodes:', workflow.nodes);\n      setWorkflow(workflow);\n      toast.success('Workflow loaded successfully');\n    } catch (error) {\n      console.error('Error loading workflow:', error);\n      setError('Failed to load workflow');\n      toast.error('Failed to load workflow');\n    }\n  };\n  const createNewWorkflow = () => {\n    setWorkflow({\n      id: null,\n      name: 'Untitled Workflow',\n      description: '',\n      nodes: [],\n      edges: [],\n      viewport: {\n        x: 0,\n        y: 0,\n        zoom: 1\n      }\n    });\n  };\n  const saveWorkflow = async () => {\n    try {\n      const workflowData = {\n        name: 'My Workflow',\n        // This should come from a form or state\n        description: 'Workflow description',\n        nodes,\n        edges,\n        viewport: (reactFlowInstance === null || reactFlowInstance === void 0 ? void 0 : reactFlowInstance.getViewport()) || {\n          x: 0,\n          y: 0,\n          zoom: 1\n        }\n      };\n      let savedWorkflow;\n      if (id) {\n        savedWorkflow = await workflowAPI.updateWorkflow(id, workflowData);\n        toast.success('Workflow saved successfully');\n      } else {\n        savedWorkflow = await workflowAPI.createWorkflow(workflowData);\n        navigate(`/workflow/${savedWorkflow.id}`, {\n          replace: true\n        });\n        toast.success('Workflow created successfully');\n      }\n      setWorkflow(savedWorkflow);\n    } catch (error) {\n      console.error('Error saving workflow:', error);\n      toast.error('Failed to save workflow');\n    }\n  };\n  const onDragOver = useCallback(event => {\n    event.preventDefault();\n    event.dataTransfer.dropEffect = 'move';\n  }, []);\n  const onDrop = useCallback(event => {\n    event.preventDefault();\n    const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();\n    const nodeData = JSON.parse(event.dataTransfer.getData('application/reactflow'));\n    if (typeof nodeData === 'undefined' || !nodeData) {\n      return;\n    }\n    const position = project({\n      x: event.clientX - reactFlowBounds.left,\n      y: event.clientY - reactFlowBounds.top\n    });\n    const newNode = {\n      id: `${nodeData.type}_${Date.now()}`,\n      type: nodeData.type,\n      position,\n      data: {\n        ...nodeData,\n        status: 'pending',\n        progress: 0\n      }\n    };\n    addNode(newNode);\n    if (id) {\n      emitNodeUpdate(id, {\n        type: 'add',\n        node: newNode\n      });\n    }\n  }, [project, addNode, id, emitNodeUpdate]);\n  const onSelectionChange = useCallback(({\n    nodes,\n    edges\n  }) => {\n    setSelectedNodes(nodes);\n    setSelectedEdges(edges);\n  }, []);\n  const handleNodeUpdate = useCallback((nodeId, updates) => {\n    updateNode(nodeId, updates);\n    if (id) {\n      emitNodeUpdate(id, {\n        type: 'update',\n        nodeId,\n        updates\n      });\n    }\n  }, [updateNode, id, emitNodeUpdate]);\n  const handleEdgeUpdate = useCallback((edgeId, updates) => {\n    updateEdge(edgeId, updates);\n    if (id) {\n      emitEdgeUpdate(id, {\n        type: 'update',\n        edgeId,\n        updates\n      });\n    }\n  }, [updateEdge, id, emitEdgeUpdate]);\n  const onConnectHandler = useCallback(params => {\n    const newEdge = {\n      ...params,\n      id: `edge_${Date.now()}`,\n      type: 'custom',\n      data: {\n        label: '',\n        color: '#64748b'\n      }\n    };\n    onConnect(newEdge);\n    if (id) {\n      emitEdgeUpdate(id, {\n        type: 'add',\n        edge: newEdge\n      });\n    }\n  }, [onConnect, id, emitEdgeUpdate]);\n\n  // Context menu handlers\n  const onNodeContextMenu = useCallback((event, node) => {\n    var _node$data;\n    console.log('🚀 RIGHT-CLICK DETECTED!', node);\n    alert(`Right-clicked on node: ${(node === null || node === void 0 ? void 0 : (_node$data = node.data) === null || _node$data === void 0 ? void 0 : _node$data.label) || (node === null || node === void 0 ? void 0 : node.id) || 'Unknown'}`);\n    console.log('🖱️ Right-click on node:', node);\n    console.log('🖱️ Node type:', typeof node);\n    console.log('🖱️ Node is null/undefined:', node == null);\n    console.log('🖱️ Event details:', {\n      clientX: event.clientX,\n      clientY: event.clientY\n    });\n    event.preventDefault();\n    event.stopPropagation();\n    if (!node) {\n      console.log('❌ Node is null/undefined, cannot show context menu');\n      return;\n    }\n    const menuState = {\n      isVisible: true,\n      position: {\n        x: event.clientX,\n        y: event.clientY\n      },\n      node\n    };\n    console.log('🖱️ Setting context menu state:', menuState);\n    setContextMenu(menuState);\n  }, []);\n  const closeContextMenu = useCallback(() => {\n    console.log('🖱️ Closing context menu');\n    setContextMenu({\n      isVisible: false,\n      position: {\n        x: 0,\n        y: 0\n      },\n      node: null\n    });\n  }, []);\n  const handleEditConfig = useCallback(node => {\n    setConfigModal({\n      isOpen: true,\n      node\n    });\n  }, []);\n  const handleNewRun = useCallback(node => {\n    var _node$data2;\n    console.log('🚀 handleNewRun called with node:', node);\n    console.log('🚀 Current nodes count:', nodes.length);\n    console.log('🚀 Current edges count:', edges.length);\n    const validation = validateBranchCreation(node, nodes, edges);\n    console.log('🚀 Validation result:', validation);\n    if (!validation.isValid) {\n      console.log('❌ Validation failed:', validation.errors);\n      validation.errors.forEach(error => toast.error(error));\n      return;\n    }\n    const {\n      newNodes,\n      newEdges\n    } = createNewBranch(nodes, edges, node.id);\n    console.log('🚀 Branch creation result:', {\n      newNodesCount: newNodes.length,\n      newEdgesCount: newEdges.length\n    });\n    if (newNodes.length === 0) {\n      console.log('❌ No new nodes created');\n      toast.error('Could not create branch from this node');\n      return;\n    }\n    console.log('✅ Adding nodes and edges to workflow');\n    // Add new nodes and edges to the workflow\n    newNodes.forEach(newNode => {\n      console.log('➕ Adding node:', newNode.id, newNode.type);\n      addNode(newNode);\n    });\n    newEdges.forEach(newEdge => {\n      console.log('➕ Adding edge:', newEdge.id);\n      addEdge(newEdge);\n    });\n    toast.success(`New branch created from ${((_node$data2 = node.data) === null || _node$data2 === void 0 ? void 0 : _node$data2.label) || node.id}`);\n\n    // Emit updates for real-time collaboration\n    if (id) {\n      newNodes.forEach(newNode => {\n        emitNodeUpdate(id, {\n          type: 'add',\n          node: newNode\n        });\n      });\n      newEdges.forEach(newEdge => {\n        emitEdgeUpdate(id, {\n          type: 'add',\n          edge: newEdge\n        });\n      });\n    }\n  }, [nodes, edges, addNode, addEdge, id, emitNodeUpdate, emitEdgeUpdate]);\n  const handleDuplicateNode = useCallback(node => {\n    var _duplicatedNode$data;\n    const duplicatedNode = duplicateNode(node, nodes);\n    addNode(duplicatedNode);\n    toast.success(`Node duplicated: ${(_duplicatedNode$data = duplicatedNode.data) === null || _duplicatedNode$data === void 0 ? void 0 : _duplicatedNode$data.label}`);\n    if (id) {\n      emitNodeUpdate(id, {\n        type: 'add',\n        node: duplicatedNode\n      });\n    }\n  }, [nodes, addNode, id, emitNodeUpdate]);\n  const handleDeleteNode = useCallback(node => {\n    var _node$data3;\n    if (window.confirm(`Are you sure you want to delete \"${((_node$data3 = node.data) === null || _node$data3 === void 0 ? void 0 : _node$data3.label) || node.id}\"?`)) {\n      var _node$data4;\n      // Remove connected edges first\n      const connectedEdges = edges.filter(edge => edge.source === node.id || edge.target === node.id);\n      connectedEdges.forEach(edge => {\n        onEdgesChange([{\n          type: 'remove',\n          id: edge.id\n        }]);\n      });\n\n      // Remove the node\n      onNodesChange([{\n        type: 'remove',\n        id: node.id\n      }]);\n      toast.success(`Node deleted: ${((_node$data4 = node.data) === null || _node$data4 === void 0 ? void 0 : _node$data4.label) || node.id}`);\n      if (id) {\n        emitNodeUpdate(id, {\n          type: 'delete',\n          nodeId: node.id\n        });\n      }\n    }\n  }, [edges, onEdgesChange, onNodesChange, id, emitNodeUpdate]);\n  const handleSaveConfig = useCallback((nodeId, config) => {\n    handleNodeUpdate(nodeId, {\n      data: config\n    });\n    toast.success('Configuration saved successfully');\n  }, [handleNodeUpdate]);\n  const closeConfigModal = useCallback(() => {\n    setConfigModal({\n      isOpen: false,\n      node: null\n    });\n  }, []);\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-screen\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 388,\n      columnNumber: 7\n    }, this);\n  }\n  const theme = getCurrentTheme();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen\",\n    style: {\n      backgroundColor: 'var(--color-primary)'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Sidebar, {\n      selectedNode: selectedNodes[0],\n      selectedEdge: selectedEdges[0],\n      onUpdateNode: handleNodeUpdate,\n      onUpdateEdge: handleEdgeUpdate,\n      isCollapsed: sidebarCollapsed,\n      onToggleCollapse: () => setSidebarCollapsed(!sidebarCollapsed)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 398,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col\",\n      children: [/*#__PURE__*/_jsxDEV(Toolbar, {\n        onSave: saveWorkflow\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 408,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1\",\n        ref: reactFlowWrapper,\n        children: /*#__PURE__*/_jsxDEV(ReactFlow, {\n          nodes: nodes,\n          edges: edges,\n          onNodesChange: onNodesChange,\n          onEdgesChange: onEdgesChange,\n          onConnect: onConnectHandler,\n          onInit: setReactFlowInstance,\n          onDrop: onDrop,\n          onDragOver: onDragOver,\n          onSelectionChange: onSelectionChange,\n          onNodeClick: (event, node) => {\n            var _node$data5;\n            console.log('🖱️ Node clicked:', node);\n            alert(`Clicked on node: ${(node === null || node === void 0 ? void 0 : (_node$data5 = node.data) === null || _node$data5 === void 0 ? void 0 : _node$data5.label) || (node === null || node === void 0 ? void 0 : node.id)}`);\n          },\n          onNodeContextMenu: onNodeContextMenu,\n          onPaneContextMenu: event => {\n            console.log('🖱️ Right-click on pane (background)');\n            event.preventDefault();\n          },\n          nodeTypes: nodeTypes,\n          edgeTypes: edgeTypes,\n          fitView: true,\n          attributionPosition: \"bottom-left\",\n          style: {\n            backgroundColor: theme.colors.secondary\n          },\n          children: [/*#__PURE__*/_jsxDEV(Background, {\n            color: theme.colors.border,\n            gap: 20,\n            size: 1,\n            variant: \"dots\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Controls, {\n            className: \"themed-card rounded-lg\",\n            style: {\n              backgroundColor: `${theme.colors.cardBg}dd`,\n              backdropFilter: 'blur(8px)'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 442,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(MiniMap, {\n            className: \"themed-card rounded-lg\",\n            style: {\n              backgroundColor: `${theme.colors.cardBg}dd`,\n              backdropFilter: 'blur(8px)'\n            },\n            nodeColor: node => {\n              switch (node.type) {\n                case 'input':\n                  return theme.colors.success;\n                case 'output':\n                  return theme.colors.brand;\n                case 'process':\n                  return theme.colors.info;\n                case 'decision':\n                  return theme.colors.warning;\n                default:\n                  return theme.colors.textTertiary;\n              }\n            },\n            maskColor: `${theme.colors.primary}1a`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 407,\n      columnNumber: 7\n    }, this), console.log('🎯 Rendering ContextMenu with state:', contextMenu), /*#__PURE__*/_jsxDEV(ContextMenu, {\n      isVisible: contextMenu.isVisible,\n      position: contextMenu.position,\n      selectedNode: contextMenu.node,\n      onClose: closeContextMenu,\n      onEditConfig: handleEditConfig,\n      onNewRun: handleNewRun,\n      onDuplicate: handleDuplicateNode,\n      onDelete: handleDeleteNode\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 472,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ConfigModal, {\n      isOpen: configModal.isOpen,\n      node: configModal.node,\n      onClose: closeConfigModal,\n      onSave: handleSaveConfig\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 484,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 397,\n    columnNumber: 5\n  }, this);\n};\n_s(WorkflowEditorContent, \"yyOWC8uMu6XNwNEPikgc69QkpTg=\", false, function () {\n  return [useParams, useNavigate, useReactFlow, useTheme, useWorkflow, useSocket];\n});\n_c = WorkflowEditorContent;\nconst WorkflowEditor = () => {\n  return /*#__PURE__*/_jsxDEV(ReactFlowProvider, {\n    children: /*#__PURE__*/_jsxDEV(WorkflowEditorContent, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 497,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 496,\n    columnNumber: 5\n  }, this);\n};\n_c2 = WorkflowEditor;\nexport default WorkflowEditor;\nvar _c, _c2;\n$RefreshReg$(_c, \"WorkflowEditorContent\");\n$RefreshReg$(_c2, \"WorkflowEditor\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "useRef", "useEffect", "useParams", "useNavigate", "ReactFlow", "Background", "Controls", "MiniMap", "useReactFlow", "ReactFlowProvider", "toast", "nodeTypes", "edgeTypes", "Sidebar", "<PERSON><PERSON><PERSON>", "ContextMenu", "useTheme", "ConfigModal", "useWorkflow", "useSocket", "workflowAPI", "createNewBranch", "duplicateNode", "validateBranchCreation", "jsxDEV", "_jsxDEV", "WorkflowEditorContent", "_s", "id", "navigate", "reactFlowWrapper", "project", "getCurrentTheme", "nodes", "edges", "onNodesChange", "onEdgesChange", "onConnect", "setWorkflow", "addNode", "addEdge", "updateNode", "updateEdge", "setLoading", "setError", "isLoading", "joinWorkflow", "leaveWorkflow", "emitNodeUpdate", "emitEdgeUpdate", "socket", "selectedNodes", "setSelectedNodes", "<PERSON><PERSON><PERSON>", "setSelectedEdges", "sidebarCollapsed", "setSidebarCollapsed", "reactFlowInstance", "setReactFlowInstance", "contextMenu", "setContextMenu", "isVisible", "position", "x", "y", "node", "configModal", "setConfigModal", "isOpen", "loadWorkflow", "createNewWorkflow", "handleNodeUpdate", "data", "nodeId", "updates", "handleEdgeUpdate", "edgeId", "handleWorkflowUpdated", "workflow", "success", "on", "off", "workflowId", "getWorkflow", "console", "log", "error", "name", "description", "viewport", "zoom", "saveWorkflow", "workflowData", "getViewport", "savedWorkflow", "updateWorkflow", "createWorkflow", "replace", "onDragOver", "event", "preventDefault", "dataTransfer", "dropEffect", "onDrop", "reactFlowBounds", "current", "getBoundingClientRect", "nodeData", "JSON", "parse", "getData", "clientX", "left", "clientY", "top", "newNode", "type", "Date", "now", "status", "progress", "onSelectionChange", "onConnectHandler", "params", "newEdge", "label", "color", "edge", "onNodeContextMenu", "_node$data", "alert", "stopPropagation", "menuState", "closeContextMenu", "handleEditConfig", "handleNewRun", "_node$data2", "length", "validation", "<PERSON><PERSON><PERSON><PERSON>", "errors", "for<PERSON>ach", "newNodes", "newEdges", "newNodesCount", "newEdgesCount", "handleDuplicateNode", "_duplicatedNode$data", "duplicatedNode", "handleDeleteNode", "_node$data3", "window", "confirm", "_node$data4", "connectedEdges", "filter", "source", "target", "handleSaveConfig", "config", "closeConfigModal", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "theme", "style", "backgroundColor", "selectedNode", "selected<PERSON><PERSON>", "onUpdateNode", "onUpdateEdge", "isCollapsed", "onToggleCollapse", "onSave", "ref", "onInit", "onNodeClick", "_node$data5", "onPaneContextMenu", "<PERSON><PERSON><PERSON><PERSON>", "attributionPosition", "colors", "secondary", "border", "gap", "size", "variant", "cardBg", "<PERSON><PERSON>ilter", "nodeColor", "brand", "info", "warning", "textTertiary", "maskColor", "primary", "onClose", "onEditConfig", "onNewRun", "onDuplicate", "onDelete", "_c", "WorkflowEditor", "_c2", "$RefreshReg$"], "sources": ["/home/<USER>/vlsi-workflow/client/src/components/WorkflowEditor.js"], "sourcesContent": ["import React, { useState, useCallback, useRef, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport ReactFlow, {\n  Background,\n  Controls,\n  MiniMap,\n  useReactFlow,\n  ReactFlowProvider,\n} from 'reactflow';\nimport 'reactflow/dist/style.css';\nimport toast from 'react-hot-toast';\n\nimport { nodeTypes } from './nodes';\nimport { edgeTypes } from './edges';\nimport Sidebar from './sidebar/Sidebar';\nimport Toolbar from './Toolbar';\nimport ContextMenu from './ContextMenu';\nimport { useTheme } from '../contexts/ThemeContext';\nimport ConfigModal from './ConfigModal';\nimport { useWorkflow } from '../contexts/WorkflowContext';\nimport { useSocket } from '../contexts/SocketContext';\nimport { workflowAPI } from '../services/api';\nimport { \n  createNewBranch, \n  duplicateNode, \n  validateBranchCreation \n} from '../utils/workflowBranching';\n\nconst WorkflowEditorContent = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const reactFlowWrapper = useRef(null);\n  const { project } = useReactFlow();\n  const { getCurrentTheme } = useTheme();\n  \n  const {\n    nodes,\n    edges,\n    onNodesChange,\n    onEdgesChange,\n    onConnect,\n    setWorkflow,\n    addNode,\n    addEdge,\n    updateNode,\n    updateEdge,\n    setLoading,\n    setError,\n    isLoading,\n  } = useWorkflow();\n\n  const { \n    joinWorkflow, \n    leaveWorkflow, \n    emitNodeUpdate, \n    emitEdgeUpdate,\n    socket \n  } = useSocket();\n\n  const [selectedNodes, setSelectedNodes] = useState([]);\n  const [selectedEdges, setSelectedEdges] = useState([]);\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\n  const [reactFlowInstance, setReactFlowInstance] = useState(null);\n  \n  // Context menu state\n  const [contextMenu, setContextMenu] = useState({\n    isVisible: false,\n    position: { x: 0, y: 0 },\n    node: null\n  });\n  \n  // Config modal state\n  const [configModal, setConfigModal] = useState({\n    isOpen: false,\n    node: null\n  });\n\n  // Load workflow on mount\n  useEffect(() => {\n    if (id) {\n      loadWorkflow(id);\n      joinWorkflow(id);\n    } else {\n      // Create new workflow\n      createNewWorkflow();\n    }\n\n    return () => {\n      if (id) {\n        leaveWorkflow(id);\n      }\n    };\n  }, [id]);\n\n  // Socket event listeners\n  useEffect(() => {\n    if (!socket) return;\n\n    const handleNodeUpdate = (data) => {\n      updateNode(data.nodeId, data.updates);\n    };\n\n    const handleEdgeUpdate = (data) => {\n      updateEdge(data.edgeId, data.updates);\n    };\n\n    const handleWorkflowUpdated = (workflow) => {\n      setWorkflow(workflow);\n      toast.success('Workflow updated by collaborator');\n    };\n\n    socket.on('nodeUpdate', handleNodeUpdate);\n    socket.on('edgeUpdate', handleEdgeUpdate);\n    socket.on('workflowUpdated', handleWorkflowUpdated);\n\n    return () => {\n      socket.off('nodeUpdate', handleNodeUpdate);\n      socket.off('edgeUpdate', handleEdgeUpdate);\n      socket.off('workflowUpdated', handleWorkflowUpdated);\n    };\n  }, [socket, updateNode, updateEdge, setWorkflow]);\n\n  const loadWorkflow = async (workflowId) => {\n    try {\n      setLoading(true);\n      const workflow = await workflowAPI.getWorkflow(workflowId);\n      console.log('🔄 Loaded workflow:', workflow);\n      console.log('🔄 Workflow nodes:', workflow.nodes);\n      setWorkflow(workflow);\n      toast.success('Workflow loaded successfully');\n    } catch (error) {\n      console.error('Error loading workflow:', error);\n      setError('Failed to load workflow');\n      toast.error('Failed to load workflow');\n    }\n  };\n\n  const createNewWorkflow = () => {\n    setWorkflow({\n      id: null,\n      name: 'Untitled Workflow',\n      description: '',\n      nodes: [],\n      edges: [],\n      viewport: { x: 0, y: 0, zoom: 1 },\n    });\n  };\n\n  const saveWorkflow = async () => {\n    try {\n      const workflowData = {\n        name: 'My Workflow', // This should come from a form or state\n        description: 'Workflow description',\n        nodes,\n        edges,\n        viewport: reactFlowInstance?.getViewport() || { x: 0, y: 0, zoom: 1 },\n      };\n\n      let savedWorkflow;\n      if (id) {\n        savedWorkflow = await workflowAPI.updateWorkflow(id, workflowData);\n        toast.success('Workflow saved successfully');\n      } else {\n        savedWorkflow = await workflowAPI.createWorkflow(workflowData);\n        navigate(`/workflow/${savedWorkflow.id}`, { replace: true });\n        toast.success('Workflow created successfully');\n      }\n      \n      setWorkflow(savedWorkflow);\n    } catch (error) {\n      console.error('Error saving workflow:', error);\n      toast.error('Failed to save workflow');\n    }\n  };\n\n  const onDragOver = useCallback((event) => {\n    event.preventDefault();\n    event.dataTransfer.dropEffect = 'move';\n  }, []);\n\n  const onDrop = useCallback(\n    (event) => {\n      event.preventDefault();\n\n      const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();\n      const nodeData = JSON.parse(event.dataTransfer.getData('application/reactflow'));\n\n      if (typeof nodeData === 'undefined' || !nodeData) {\n        return;\n      }\n\n      const position = project({\n        x: event.clientX - reactFlowBounds.left,\n        y: event.clientY - reactFlowBounds.top,\n      });\n\n      const newNode = {\n        id: `${nodeData.type}_${Date.now()}`,\n        type: nodeData.type,\n        position,\n        data: {\n          ...nodeData,\n          status: 'pending',\n          progress: 0,\n        },\n      };\n\n      addNode(newNode);\n      \n      if (id) {\n        emitNodeUpdate(id, { type: 'add', node: newNode });\n      }\n    },\n    [project, addNode, id, emitNodeUpdate]\n  );\n\n  const onSelectionChange = useCallback(({ nodes, edges }) => {\n    setSelectedNodes(nodes);\n    setSelectedEdges(edges);\n  }, []);\n\n  const handleNodeUpdate = useCallback((nodeId, updates) => {\n    updateNode(nodeId, updates);\n    \n    if (id) {\n      emitNodeUpdate(id, { type: 'update', nodeId, updates });\n    }\n  }, [updateNode, id, emitNodeUpdate]);\n\n  const handleEdgeUpdate = useCallback((edgeId, updates) => {\n    updateEdge(edgeId, updates);\n    \n    if (id) {\n      emitEdgeUpdate(id, { type: 'update', edgeId, updates });\n    }\n  }, [updateEdge, id, emitEdgeUpdate]);\n\n  const onConnectHandler = useCallback((params) => {\n    const newEdge = {\n      ...params,\n      id: `edge_${Date.now()}`,\n      type: 'custom',\n      data: { label: '', color: '#64748b' },\n    };\n    \n    onConnect(newEdge);\n    \n    if (id) {\n      emitEdgeUpdate(id, { type: 'add', edge: newEdge });\n    }\n  }, [onConnect, id, emitEdgeUpdate]);\n\n  // Context menu handlers\n  const onNodeContextMenu = useCallback((event, node) => {\n    console.log('🚀 RIGHT-CLICK DETECTED!', node);\n    alert(`Right-clicked on node: ${node?.data?.label || node?.id || 'Unknown'}`);\n    console.log('🖱️ Right-click on node:', node);\n    console.log('🖱️ Node type:', typeof node);\n    console.log('🖱️ Node is null/undefined:', node == null);\n    console.log('🖱️ Event details:', { clientX: event.clientX, clientY: event.clientY });\n    event.preventDefault();\n    event.stopPropagation();\n\n    if (!node) {\n      console.log('❌ Node is null/undefined, cannot show context menu');\n      return;\n    }\n\n    const menuState = {\n      isVisible: true,\n      position: { x: event.clientX, y: event.clientY },\n      node\n    };\n    console.log('🖱️ Setting context menu state:', menuState);\n    setContextMenu(menuState);\n  }, []);\n\n  const closeContextMenu = useCallback(() => {\n    console.log('🖱️ Closing context menu');\n    setContextMenu({\n      isVisible: false,\n      position: { x: 0, y: 0 },\n      node: null\n    });\n  }, []);\n\n  const handleEditConfig = useCallback((node) => {\n    setConfigModal({\n      isOpen: true,\n      node\n    });\n  }, []);\n\n  const handleNewRun = useCallback((node) => {\n    console.log('🚀 handleNewRun called with node:', node);\n    console.log('🚀 Current nodes count:', nodes.length);\n    console.log('🚀 Current edges count:', edges.length);\n    \n    const validation = validateBranchCreation(node, nodes, edges);\n    console.log('🚀 Validation result:', validation);\n\n    if (!validation.isValid) {\n      console.log('❌ Validation failed:', validation.errors);\n      validation.errors.forEach(error => toast.error(error));\n      return;\n    }\n\n    const { newNodes, newEdges } = createNewBranch(nodes, edges, node.id);\n    console.log('🚀 Branch creation result:', { newNodesCount: newNodes.length, newEdgesCount: newEdges.length });\n\n    if (newNodes.length === 0) {\n      console.log('❌ No new nodes created');\n      toast.error('Could not create branch from this node');\n      return;\n    }\n\n    console.log('✅ Adding nodes and edges to workflow');\n    // Add new nodes and edges to the workflow\n    newNodes.forEach(newNode => {\n      console.log('➕ Adding node:', newNode.id, newNode.type);\n      addNode(newNode);\n    });\n    newEdges.forEach(newEdge => {\n      console.log('➕ Adding edge:', newEdge.id);\n      addEdge(newEdge);\n    });\n\n    toast.success(`New branch created from ${node.data?.label || node.id}`);\n\n    // Emit updates for real-time collaboration\n    if (id) {\n      newNodes.forEach(newNode => {\n        emitNodeUpdate(id, { type: 'add', node: newNode });\n      });\n      newEdges.forEach(newEdge => {\n        emitEdgeUpdate(id, { type: 'add', edge: newEdge });\n      });\n    }\n  }, [nodes, edges, addNode, addEdge, id, emitNodeUpdate, emitEdgeUpdate]);\n\n  const handleDuplicateNode = useCallback((node) => {\n    const duplicatedNode = duplicateNode(node, nodes);\n    addNode(duplicatedNode);\n    \n    toast.success(`Node duplicated: ${duplicatedNode.data?.label}`);\n    \n    if (id) {\n      emitNodeUpdate(id, { type: 'add', node: duplicatedNode });\n    }\n  }, [nodes, addNode, id, emitNodeUpdate]);\n\n  const handleDeleteNode = useCallback((node) => {\n    if (window.confirm(`Are you sure you want to delete \"${node.data?.label || node.id}\"?`)) {\n      // Remove connected edges first\n      const connectedEdges = edges.filter(edge => \n        edge.source === node.id || edge.target === node.id\n      );\n      \n      connectedEdges.forEach(edge => {\n        onEdgesChange([{ type: 'remove', id: edge.id }]);\n      });\n      \n      // Remove the node\n      onNodesChange([{ type: 'remove', id: node.id }]);\n      \n      toast.success(`Node deleted: ${node.data?.label || node.id}`);\n      \n      if (id) {\n        emitNodeUpdate(id, { type: 'delete', nodeId: node.id });\n      }\n    }\n  }, [edges, onEdgesChange, onNodesChange, id, emitNodeUpdate]);\n\n  const handleSaveConfig = useCallback((nodeId, config) => {\n    handleNodeUpdate(nodeId, { data: config });\n    toast.success('Configuration saved successfully');\n  }, [handleNodeUpdate]);\n\n  const closeConfigModal = useCallback(() => {\n    setConfigModal({\n      isOpen: false,\n      node: null\n    });\n  }, []);\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center h-screen\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500\"></div>\n      </div>\n    );\n  }\n\n  const theme = getCurrentTheme();\n  \n  return (\n    <div className=\"flex h-screen\" style={{ backgroundColor: 'var(--color-primary)' }}>\n      <Sidebar\n        selectedNode={selectedNodes[0]}\n        selectedEdge={selectedEdges[0]}\n        onUpdateNode={handleNodeUpdate}\n        onUpdateEdge={handleEdgeUpdate}\n        isCollapsed={sidebarCollapsed}\n        onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}\n      />\n      \n      <div className=\"flex-1 flex flex-col\">\n        <Toolbar onSave={saveWorkflow} />\n        \n        <div className=\"flex-1\" ref={reactFlowWrapper}>\n          <ReactFlow\n            nodes={nodes}\n            edges={edges}\n            onNodesChange={onNodesChange}\n            onEdgesChange={onEdgesChange}\n            onConnect={onConnectHandler}\n            onInit={setReactFlowInstance}\n            onDrop={onDrop}\n            onDragOver={onDragOver}\n            onSelectionChange={onSelectionChange}\n            onNodeClick={(event, node) => {\n              console.log('🖱️ Node clicked:', node);\n              alert(`Clicked on node: ${node?.data?.label || node?.id}`);\n            }}\n            onNodeContextMenu={onNodeContextMenu}\n            onPaneContextMenu={(event) => {\n              console.log('🖱️ Right-click on pane (background)');\n              event.preventDefault();\n            }}\n            nodeTypes={nodeTypes}\n            edgeTypes={edgeTypes}\n            fitView\n            attributionPosition=\"bottom-left\"\n            style={{ backgroundColor: theme.colors.secondary }}\n          >\n            <Background \n              color={theme.colors.border}\n              gap={20} \n              size={1}\n              variant=\"dots\"\n            />\n            <Controls \n              className=\"themed-card rounded-lg\"\n              style={{ \n                backgroundColor: `${theme.colors.cardBg}dd`,\n                backdropFilter: 'blur(8px)'\n              }}\n            />\n            <MiniMap \n              className=\"themed-card rounded-lg\"\n              style={{ \n                backgroundColor: `${theme.colors.cardBg}dd`,\n                backdropFilter: 'blur(8px)'\n              }}\n              nodeColor={(node) => {\n                switch (node.type) {\n                  case 'input': return theme.colors.success;\n                  case 'output': return theme.colors.brand;\n                  case 'process': return theme.colors.info;\n                  case 'decision': return theme.colors.warning;\n                  default: return theme.colors.textTertiary;\n                }\n              }}\n              maskColor={`${theme.colors.primary}1a`}\n            />\n          </ReactFlow>\n        </div>\n      </div>\n\n      {/* Context Menu */}\n      {console.log('🎯 Rendering ContextMenu with state:', contextMenu)}\n      <ContextMenu\n        isVisible={contextMenu.isVisible}\n        position={contextMenu.position}\n        selectedNode={contextMenu.node}\n        onClose={closeContextMenu}\n        onEditConfig={handleEditConfig}\n        onNewRun={handleNewRun}\n        onDuplicate={handleDuplicateNode}\n        onDelete={handleDeleteNode}\n      />\n\n      {/* Configuration Modal */}\n      <ConfigModal\n        isOpen={configModal.isOpen}\n        node={configModal.node}\n        onClose={closeConfigModal}\n        onSave={handleSaveConfig}\n      />\n    </div>\n  );\n};\n\nconst WorkflowEditor = () => {\n  return (\n    <ReactFlowProvider>\n      <WorkflowEditorContent />\n    </ReactFlowProvider>\n  );\n};\n\nexport default WorkflowEditor;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACvE,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,OAAOC,SAAS,IACdC,UAAU,EACVC,QAAQ,EACRC,OAAO,EACPC,YAAY,EACZC,iBAAiB,QACZ,WAAW;AAClB,OAAO,0BAA0B;AACjC,OAAOC,KAAK,MAAM,iBAAiB;AAEnC,SAASC,SAAS,QAAQ,SAAS;AACnC,SAASC,SAAS,QAAQ,SAAS;AACnC,OAAOC,OAAO,MAAM,mBAAmB;AACvC,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAASC,WAAW,QAAQ,iBAAiB;AAC7C,SACEC,eAAe,EACfC,aAAa,EACbC,sBAAsB,QACjB,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM;IAAEC;EAAG,CAAC,GAAG1B,SAAS,CAAC,CAAC;EAC1B,MAAM2B,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAC9B,MAAM2B,gBAAgB,GAAG9B,MAAM,CAAC,IAAI,CAAC;EACrC,MAAM;IAAE+B;EAAQ,CAAC,GAAGvB,YAAY,CAAC,CAAC;EAClC,MAAM;IAAEwB;EAAgB,CAAC,GAAGhB,QAAQ,CAAC,CAAC;EAEtC,MAAM;IACJiB,KAAK;IACLC,KAAK;IACLC,aAAa;IACbC,aAAa;IACbC,SAAS;IACTC,WAAW;IACXC,OAAO;IACPC,OAAO;IACPC,UAAU;IACVC,UAAU;IACVC,UAAU;IACVC,QAAQ;IACRC;EACF,CAAC,GAAG3B,WAAW,CAAC,CAAC;EAEjB,MAAM;IACJ4B,YAAY;IACZC,aAAa;IACbC,cAAc;IACdC,cAAc;IACdC;EACF,CAAC,GAAG/B,SAAS,CAAC,CAAC;EAEf,MAAM,CAACgC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACuD,aAAa,EAAEC,gBAAgB,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACyD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC2D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC;;EAEhE;EACA,MAAM,CAAC6D,WAAW,EAAEC,cAAc,CAAC,GAAG9D,QAAQ,CAAC;IAC7C+D,SAAS,EAAE,KAAK;IAChBC,QAAQ,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IACxBC,IAAI,EAAE;EACR,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGrE,QAAQ,CAAC;IAC7CsE,MAAM,EAAE,KAAK;IACbH,IAAI,EAAE;EACR,CAAC,CAAC;;EAEF;EACAhE,SAAS,CAAC,MAAM;IACd,IAAI2B,EAAE,EAAE;MACNyC,YAAY,CAACzC,EAAE,CAAC;MAChBkB,YAAY,CAAClB,EAAE,CAAC;IAClB,CAAC,MAAM;MACL;MACA0C,iBAAiB,CAAC,CAAC;IACrB;IAEA,OAAO,MAAM;MACX,IAAI1C,EAAE,EAAE;QACNmB,aAAa,CAACnB,EAAE,CAAC;MACnB;IACF,CAAC;EACH,CAAC,EAAE,CAACA,EAAE,CAAC,CAAC;;EAER;EACA3B,SAAS,CAAC,MAAM;IACd,IAAI,CAACiD,MAAM,EAAE;IAEb,MAAMqB,gBAAgB,GAAIC,IAAI,IAAK;MACjC/B,UAAU,CAAC+B,IAAI,CAACC,MAAM,EAAED,IAAI,CAACE,OAAO,CAAC;IACvC,CAAC;IAED,MAAMC,gBAAgB,GAAIH,IAAI,IAAK;MACjC9B,UAAU,CAAC8B,IAAI,CAACI,MAAM,EAAEJ,IAAI,CAACE,OAAO,CAAC;IACvC,CAAC;IAED,MAAMG,qBAAqB,GAAIC,QAAQ,IAAK;MAC1CxC,WAAW,CAACwC,QAAQ,CAAC;MACrBpE,KAAK,CAACqE,OAAO,CAAC,kCAAkC,CAAC;IACnD,CAAC;IAED7B,MAAM,CAAC8B,EAAE,CAAC,YAAY,EAAET,gBAAgB,CAAC;IACzCrB,MAAM,CAAC8B,EAAE,CAAC,YAAY,EAAEL,gBAAgB,CAAC;IACzCzB,MAAM,CAAC8B,EAAE,CAAC,iBAAiB,EAAEH,qBAAqB,CAAC;IAEnD,OAAO,MAAM;MACX3B,MAAM,CAAC+B,GAAG,CAAC,YAAY,EAAEV,gBAAgB,CAAC;MAC1CrB,MAAM,CAAC+B,GAAG,CAAC,YAAY,EAAEN,gBAAgB,CAAC;MAC1CzB,MAAM,CAAC+B,GAAG,CAAC,iBAAiB,EAAEJ,qBAAqB,CAAC;IACtD,CAAC;EACH,CAAC,EAAE,CAAC3B,MAAM,EAAET,UAAU,EAAEC,UAAU,EAAEJ,WAAW,CAAC,CAAC;EAEjD,MAAM+B,YAAY,GAAG,MAAOa,UAAU,IAAK;IACzC,IAAI;MACFvC,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMmC,QAAQ,GAAG,MAAM1D,WAAW,CAAC+D,WAAW,CAACD,UAAU,CAAC;MAC1DE,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEP,QAAQ,CAAC;MAC5CM,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEP,QAAQ,CAAC7C,KAAK,CAAC;MACjDK,WAAW,CAACwC,QAAQ,CAAC;MACrBpE,KAAK,CAACqE,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C1C,QAAQ,CAAC,yBAAyB,CAAC;MACnClC,KAAK,CAAC4E,KAAK,CAAC,yBAAyB,CAAC;IACxC;EACF,CAAC;EAED,MAAMhB,iBAAiB,GAAGA,CAAA,KAAM;IAC9BhC,WAAW,CAAC;MACVV,EAAE,EAAE,IAAI;MACR2D,IAAI,EAAE,mBAAmB;MACzBC,WAAW,EAAE,EAAE;MACfvD,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTuD,QAAQ,EAAE;QAAE1B,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE,CAAC;QAAE0B,IAAI,EAAE;MAAE;IAClC,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMC,YAAY,GAAG;QACnBL,IAAI,EAAE,aAAa;QAAE;QACrBC,WAAW,EAAE,sBAAsB;QACnCvD,KAAK;QACLC,KAAK;QACLuD,QAAQ,EAAE,CAAAhC,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEoC,WAAW,CAAC,CAAC,KAAI;UAAE9B,CAAC,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;UAAE0B,IAAI,EAAE;QAAE;MACtE,CAAC;MAED,IAAII,aAAa;MACjB,IAAIlE,EAAE,EAAE;QACNkE,aAAa,GAAG,MAAM1E,WAAW,CAAC2E,cAAc,CAACnE,EAAE,EAAEgE,YAAY,CAAC;QAClElF,KAAK,CAACqE,OAAO,CAAC,6BAA6B,CAAC;MAC9C,CAAC,MAAM;QACLe,aAAa,GAAG,MAAM1E,WAAW,CAAC4E,cAAc,CAACJ,YAAY,CAAC;QAC9D/D,QAAQ,CAAC,aAAaiE,aAAa,CAAClE,EAAE,EAAE,EAAE;UAAEqE,OAAO,EAAE;QAAK,CAAC,CAAC;QAC5DvF,KAAK,CAACqE,OAAO,CAAC,+BAA+B,CAAC;MAChD;MAEAzC,WAAW,CAACwD,aAAa,CAAC;IAC5B,CAAC,CAAC,OAAOR,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C5E,KAAK,CAAC4E,KAAK,CAAC,yBAAyB,CAAC;IACxC;EACF,CAAC;EAED,MAAMY,UAAU,GAAGnG,WAAW,CAAEoG,KAAK,IAAK;IACxCA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtBD,KAAK,CAACE,YAAY,CAACC,UAAU,GAAG,MAAM;EACxC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,MAAM,GAAGxG,WAAW,CACvBoG,KAAK,IAAK;IACTA,KAAK,CAACC,cAAc,CAAC,CAAC;IAEtB,MAAMI,eAAe,GAAG1E,gBAAgB,CAAC2E,OAAO,CAACC,qBAAqB,CAAC,CAAC;IACxE,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACV,KAAK,CAACE,YAAY,CAACS,OAAO,CAAC,uBAAuB,CAAC,CAAC;IAEhF,IAAI,OAAOH,QAAQ,KAAK,WAAW,IAAI,CAACA,QAAQ,EAAE;MAChD;IACF;IAEA,MAAM7C,QAAQ,GAAG/B,OAAO,CAAC;MACvBgC,CAAC,EAAEoC,KAAK,CAACY,OAAO,GAAGP,eAAe,CAACQ,IAAI;MACvChD,CAAC,EAAEmC,KAAK,CAACc,OAAO,GAAGT,eAAe,CAACU;IACrC,CAAC,CAAC;IAEF,MAAMC,OAAO,GAAG;MACdvF,EAAE,EAAE,GAAG+E,QAAQ,CAACS,IAAI,IAAIC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MACpCF,IAAI,EAAET,QAAQ,CAACS,IAAI;MACnBtD,QAAQ;MACRU,IAAI,EAAE;QACJ,GAAGmC,QAAQ;QACXY,MAAM,EAAE,SAAS;QACjBC,QAAQ,EAAE;MACZ;IACF,CAAC;IAEDjF,OAAO,CAAC4E,OAAO,CAAC;IAEhB,IAAIvF,EAAE,EAAE;MACNoB,cAAc,CAACpB,EAAE,EAAE;QAAEwF,IAAI,EAAE,KAAK;QAAEnD,IAAI,EAAEkD;MAAQ,CAAC,CAAC;IACpD;EACF,CAAC,EACD,CAACpF,OAAO,EAAEQ,OAAO,EAAEX,EAAE,EAAEoB,cAAc,CACvC,CAAC;EAED,MAAMyE,iBAAiB,GAAG1H,WAAW,CAAC,CAAC;IAAEkC,KAAK;IAAEC;EAAM,CAAC,KAAK;IAC1DkB,gBAAgB,CAACnB,KAAK,CAAC;IACvBqB,gBAAgB,CAACpB,KAAK,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMqC,gBAAgB,GAAGxE,WAAW,CAAC,CAAC0E,MAAM,EAAEC,OAAO,KAAK;IACxDjC,UAAU,CAACgC,MAAM,EAAEC,OAAO,CAAC;IAE3B,IAAI9C,EAAE,EAAE;MACNoB,cAAc,CAACpB,EAAE,EAAE;QAAEwF,IAAI,EAAE,QAAQ;QAAE3C,MAAM;QAAEC;MAAQ,CAAC,CAAC;IACzD;EACF,CAAC,EAAE,CAACjC,UAAU,EAAEb,EAAE,EAAEoB,cAAc,CAAC,CAAC;EAEpC,MAAM2B,gBAAgB,GAAG5E,WAAW,CAAC,CAAC6E,MAAM,EAAEF,OAAO,KAAK;IACxDhC,UAAU,CAACkC,MAAM,EAAEF,OAAO,CAAC;IAE3B,IAAI9C,EAAE,EAAE;MACNqB,cAAc,CAACrB,EAAE,EAAE;QAAEwF,IAAI,EAAE,QAAQ;QAAExC,MAAM;QAAEF;MAAQ,CAAC,CAAC;IACzD;EACF,CAAC,EAAE,CAAChC,UAAU,EAAEd,EAAE,EAAEqB,cAAc,CAAC,CAAC;EAEpC,MAAMyE,gBAAgB,GAAG3H,WAAW,CAAE4H,MAAM,IAAK;IAC/C,MAAMC,OAAO,GAAG;MACd,GAAGD,MAAM;MACT/F,EAAE,EAAE,QAAQyF,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MACxBF,IAAI,EAAE,QAAQ;MACd5C,IAAI,EAAE;QAAEqD,KAAK,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAU;IACtC,CAAC;IAEDzF,SAAS,CAACuF,OAAO,CAAC;IAElB,IAAIhG,EAAE,EAAE;MACNqB,cAAc,CAACrB,EAAE,EAAE;QAAEwF,IAAI,EAAE,KAAK;QAAEW,IAAI,EAAEH;MAAQ,CAAC,CAAC;IACpD;EACF,CAAC,EAAE,CAACvF,SAAS,EAAET,EAAE,EAAEqB,cAAc,CAAC,CAAC;;EAEnC;EACA,MAAM+E,iBAAiB,GAAGjI,WAAW,CAAC,CAACoG,KAAK,EAAElC,IAAI,KAAK;IAAA,IAAAgE,UAAA;IACrD7C,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEpB,IAAI,CAAC;IAC7CiE,KAAK,CAAC,0BAA0B,CAAAjE,IAAI,aAAJA,IAAI,wBAAAgE,UAAA,GAAJhE,IAAI,CAAEO,IAAI,cAAAyD,UAAA,uBAAVA,UAAA,CAAYJ,KAAK,MAAI5D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAErC,EAAE,KAAI,SAAS,EAAE,CAAC;IAC7EwD,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEpB,IAAI,CAAC;IAC7CmB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,OAAOpB,IAAI,CAAC;IAC1CmB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEpB,IAAI,IAAI,IAAI,CAAC;IACxDmB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE;MAAE0B,OAAO,EAAEZ,KAAK,CAACY,OAAO;MAAEE,OAAO,EAAEd,KAAK,CAACc;IAAQ,CAAC,CAAC;IACrFd,KAAK,CAACC,cAAc,CAAC,CAAC;IACtBD,KAAK,CAACgC,eAAe,CAAC,CAAC;IAEvB,IAAI,CAAClE,IAAI,EAAE;MACTmB,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;MACjE;IACF;IAEA,MAAM+C,SAAS,GAAG;MAChBvE,SAAS,EAAE,IAAI;MACfC,QAAQ,EAAE;QAAEC,CAAC,EAAEoC,KAAK,CAACY,OAAO;QAAE/C,CAAC,EAAEmC,KAAK,CAACc;MAAQ,CAAC;MAChDhD;IACF,CAAC;IACDmB,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE+C,SAAS,CAAC;IACzDxE,cAAc,CAACwE,SAAS,CAAC;EAC3B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,gBAAgB,GAAGtI,WAAW,CAAC,MAAM;IACzCqF,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IACvCzB,cAAc,CAAC;MACbC,SAAS,EAAE,KAAK;MAChBC,QAAQ,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAE;MAAE,CAAC;MACxBC,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMqE,gBAAgB,GAAGvI,WAAW,CAAEkE,IAAI,IAAK;IAC7CE,cAAc,CAAC;MACbC,MAAM,EAAE,IAAI;MACZH;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMsE,YAAY,GAAGxI,WAAW,CAAEkE,IAAI,IAAK;IAAA,IAAAuE,WAAA;IACzCpD,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEpB,IAAI,CAAC;IACtDmB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEpD,KAAK,CAACwG,MAAM,CAAC;IACpDrD,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEnD,KAAK,CAACuG,MAAM,CAAC;IAEpD,MAAMC,UAAU,GAAGnH,sBAAsB,CAAC0C,IAAI,EAAEhC,KAAK,EAAEC,KAAK,CAAC;IAC7DkD,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEqD,UAAU,CAAC;IAEhD,IAAI,CAACA,UAAU,CAACC,OAAO,EAAE;MACvBvD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEqD,UAAU,CAACE,MAAM,CAAC;MACtDF,UAAU,CAACE,MAAM,CAACC,OAAO,CAACvD,KAAK,IAAI5E,KAAK,CAAC4E,KAAK,CAACA,KAAK,CAAC,CAAC;MACtD;IACF;IAEA,MAAM;MAAEwD,QAAQ;MAAEC;IAAS,CAAC,GAAG1H,eAAe,CAACY,KAAK,EAAEC,KAAK,EAAE+B,IAAI,CAACrC,EAAE,CAAC;IACrEwD,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE;MAAE2D,aAAa,EAAEF,QAAQ,CAACL,MAAM;MAAEQ,aAAa,EAAEF,QAAQ,CAACN;IAAO,CAAC,CAAC;IAE7G,IAAIK,QAAQ,CAACL,MAAM,KAAK,CAAC,EAAE;MACzBrD,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC3E,KAAK,CAAC4E,KAAK,CAAC,wCAAwC,CAAC;MACrD;IACF;IAEAF,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACnD;IACAyD,QAAQ,CAACD,OAAO,CAAC1B,OAAO,IAAI;MAC1B/B,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE8B,OAAO,CAACvF,EAAE,EAAEuF,OAAO,CAACC,IAAI,CAAC;MACvD7E,OAAO,CAAC4E,OAAO,CAAC;IAClB,CAAC,CAAC;IACF4B,QAAQ,CAACF,OAAO,CAACjB,OAAO,IAAI;MAC1BxC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEuC,OAAO,CAAChG,EAAE,CAAC;MACzCY,OAAO,CAACoF,OAAO,CAAC;IAClB,CAAC,CAAC;IAEFlH,KAAK,CAACqE,OAAO,CAAC,2BAA2B,EAAAyD,WAAA,GAAAvE,IAAI,CAACO,IAAI,cAAAgE,WAAA,uBAATA,WAAA,CAAWX,KAAK,KAAI5D,IAAI,CAACrC,EAAE,EAAE,CAAC;;IAEvE;IACA,IAAIA,EAAE,EAAE;MACNkH,QAAQ,CAACD,OAAO,CAAC1B,OAAO,IAAI;QAC1BnE,cAAc,CAACpB,EAAE,EAAE;UAAEwF,IAAI,EAAE,KAAK;UAAEnD,IAAI,EAAEkD;QAAQ,CAAC,CAAC;MACpD,CAAC,CAAC;MACF4B,QAAQ,CAACF,OAAO,CAACjB,OAAO,IAAI;QAC1B3E,cAAc,CAACrB,EAAE,EAAE;UAAEwF,IAAI,EAAE,KAAK;UAAEW,IAAI,EAAEH;QAAQ,CAAC,CAAC;MACpD,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC3F,KAAK,EAAEC,KAAK,EAAEK,OAAO,EAAEC,OAAO,EAAEZ,EAAE,EAAEoB,cAAc,EAAEC,cAAc,CAAC,CAAC;EAExE,MAAMiG,mBAAmB,GAAGnJ,WAAW,CAAEkE,IAAI,IAAK;IAAA,IAAAkF,oBAAA;IAChD,MAAMC,cAAc,GAAG9H,aAAa,CAAC2C,IAAI,EAAEhC,KAAK,CAAC;IACjDM,OAAO,CAAC6G,cAAc,CAAC;IAEvB1I,KAAK,CAACqE,OAAO,CAAC,qBAAAoE,oBAAA,GAAoBC,cAAc,CAAC5E,IAAI,cAAA2E,oBAAA,uBAAnBA,oBAAA,CAAqBtB,KAAK,EAAE,CAAC;IAE/D,IAAIjG,EAAE,EAAE;MACNoB,cAAc,CAACpB,EAAE,EAAE;QAAEwF,IAAI,EAAE,KAAK;QAAEnD,IAAI,EAAEmF;MAAe,CAAC,CAAC;IAC3D;EACF,CAAC,EAAE,CAACnH,KAAK,EAAEM,OAAO,EAAEX,EAAE,EAAEoB,cAAc,CAAC,CAAC;EAExC,MAAMqG,gBAAgB,GAAGtJ,WAAW,CAAEkE,IAAI,IAAK;IAAA,IAAAqF,WAAA;IAC7C,IAAIC,MAAM,CAACC,OAAO,CAAC,oCAAoC,EAAAF,WAAA,GAAArF,IAAI,CAACO,IAAI,cAAA8E,WAAA,uBAATA,WAAA,CAAWzB,KAAK,KAAI5D,IAAI,CAACrC,EAAE,IAAI,CAAC,EAAE;MAAA,IAAA6H,WAAA;MACvF;MACA,MAAMC,cAAc,GAAGxH,KAAK,CAACyH,MAAM,CAAC5B,IAAI,IACtCA,IAAI,CAAC6B,MAAM,KAAK3F,IAAI,CAACrC,EAAE,IAAImG,IAAI,CAAC8B,MAAM,KAAK5F,IAAI,CAACrC,EAClD,CAAC;MAED8H,cAAc,CAACb,OAAO,CAACd,IAAI,IAAI;QAC7B3F,aAAa,CAAC,CAAC;UAAEgF,IAAI,EAAE,QAAQ;UAAExF,EAAE,EAAEmG,IAAI,CAACnG;QAAG,CAAC,CAAC,CAAC;MAClD,CAAC,CAAC;;MAEF;MACAO,aAAa,CAAC,CAAC;QAAEiF,IAAI,EAAE,QAAQ;QAAExF,EAAE,EAAEqC,IAAI,CAACrC;MAAG,CAAC,CAAC,CAAC;MAEhDlB,KAAK,CAACqE,OAAO,CAAC,iBAAiB,EAAA0E,WAAA,GAAAxF,IAAI,CAACO,IAAI,cAAAiF,WAAA,uBAATA,WAAA,CAAW5B,KAAK,KAAI5D,IAAI,CAACrC,EAAE,EAAE,CAAC;MAE7D,IAAIA,EAAE,EAAE;QACNoB,cAAc,CAACpB,EAAE,EAAE;UAAEwF,IAAI,EAAE,QAAQ;UAAE3C,MAAM,EAAER,IAAI,CAACrC;QAAG,CAAC,CAAC;MACzD;IACF;EACF,CAAC,EAAE,CAACM,KAAK,EAAEE,aAAa,EAAED,aAAa,EAAEP,EAAE,EAAEoB,cAAc,CAAC,CAAC;EAE7D,MAAM8G,gBAAgB,GAAG/J,WAAW,CAAC,CAAC0E,MAAM,EAAEsF,MAAM,KAAK;IACvDxF,gBAAgB,CAACE,MAAM,EAAE;MAAED,IAAI,EAAEuF;IAAO,CAAC,CAAC;IAC1CrJ,KAAK,CAACqE,OAAO,CAAC,kCAAkC,CAAC;EACnD,CAAC,EAAE,CAACR,gBAAgB,CAAC,CAAC;EAEtB,MAAMyF,gBAAgB,GAAGjK,WAAW,CAAC,MAAM;IACzCoE,cAAc,CAAC;MACbC,MAAM,EAAE,KAAK;MACbH,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIpB,SAAS,EAAE;IACb,oBACEpB,OAAA;MAAKwI,SAAS,EAAC,2CAA2C;MAAAC,QAAA,eACxDzI,OAAA;QAAKwI,SAAS,EAAC;MAAgE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnF,CAAC;EAEV;EAEA,MAAMC,KAAK,GAAGvI,eAAe,CAAC,CAAC;EAE/B,oBACEP,OAAA;IAAKwI,SAAS,EAAC,eAAe;IAACO,KAAK,EAAE;MAAEC,eAAe,EAAE;IAAuB,CAAE;IAAAP,QAAA,gBAChFzI,OAAA,CAACZ,OAAO;MACN6J,YAAY,EAAEvH,aAAa,CAAC,CAAC,CAAE;MAC/BwH,YAAY,EAAEtH,aAAa,CAAC,CAAC,CAAE;MAC/BuH,YAAY,EAAErG,gBAAiB;MAC/BsG,YAAY,EAAElG,gBAAiB;MAC/BmG,WAAW,EAAEvH,gBAAiB;MAC9BwH,gBAAgB,EAAEA,CAAA,KAAMvH,mBAAmB,CAAC,CAACD,gBAAgB;IAAE;MAAA4G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChE,CAAC,eAEF7I,OAAA;MAAKwI,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACnCzI,OAAA,CAACX,OAAO;QAACkK,MAAM,EAAErF;MAAa;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEjC7I,OAAA;QAAKwI,SAAS,EAAC,QAAQ;QAACgB,GAAG,EAAEnJ,gBAAiB;QAAAoI,QAAA,eAC5CzI,OAAA,CAACrB,SAAS;UACR6B,KAAK,EAAEA,KAAM;UACbC,KAAK,EAAEA,KAAM;UACbC,aAAa,EAAEA,aAAc;UAC7BC,aAAa,EAAEA,aAAc;UAC7BC,SAAS,EAAEqF,gBAAiB;UAC5BwD,MAAM,EAAExH,oBAAqB;UAC7B6C,MAAM,EAAEA,MAAO;UACfL,UAAU,EAAEA,UAAW;UACvBuB,iBAAiB,EAAEA,iBAAkB;UACrC0D,WAAW,EAAEA,CAAChF,KAAK,EAAElC,IAAI,KAAK;YAAA,IAAAmH,WAAA;YAC5BhG,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEpB,IAAI,CAAC;YACtCiE,KAAK,CAAC,oBAAoB,CAAAjE,IAAI,aAAJA,IAAI,wBAAAmH,WAAA,GAAJnH,IAAI,CAAEO,IAAI,cAAA4G,WAAA,uBAAVA,WAAA,CAAYvD,KAAK,MAAI5D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAErC,EAAE,GAAE,CAAC;UAC5D,CAAE;UACFoG,iBAAiB,EAAEA,iBAAkB;UACrCqD,iBAAiB,EAAGlF,KAAK,IAAK;YAC5Bf,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;YACnDc,KAAK,CAACC,cAAc,CAAC,CAAC;UACxB,CAAE;UACFzF,SAAS,EAAEA,SAAU;UACrBC,SAAS,EAAEA,SAAU;UACrB0K,OAAO;UACPC,mBAAmB,EAAC,aAAa;UACjCf,KAAK,EAAE;YAAEC,eAAe,EAAEF,KAAK,CAACiB,MAAM,CAACC;UAAU,CAAE;UAAAvB,QAAA,gBAEnDzI,OAAA,CAACpB,UAAU;YACTyH,KAAK,EAAEyC,KAAK,CAACiB,MAAM,CAACE,MAAO;YAC3BC,GAAG,EAAE,EAAG;YACRC,IAAI,EAAE,CAAE;YACRC,OAAO,EAAC;UAAM;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACF7I,OAAA,CAACnB,QAAQ;YACP2J,SAAS,EAAC,wBAAwB;YAClCO,KAAK,EAAE;cACLC,eAAe,EAAE,GAAGF,KAAK,CAACiB,MAAM,CAACM,MAAM,IAAI;cAC3CC,cAAc,EAAE;YAClB;UAAE;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACF7I,OAAA,CAAClB,OAAO;YACN0J,SAAS,EAAC,wBAAwB;YAClCO,KAAK,EAAE;cACLC,eAAe,EAAE,GAAGF,KAAK,CAACiB,MAAM,CAACM,MAAM,IAAI;cAC3CC,cAAc,EAAE;YAClB,CAAE;YACFC,SAAS,EAAG/H,IAAI,IAAK;cACnB,QAAQA,IAAI,CAACmD,IAAI;gBACf,KAAK,OAAO;kBAAE,OAAOmD,KAAK,CAACiB,MAAM,CAACzG,OAAO;gBACzC,KAAK,QAAQ;kBAAE,OAAOwF,KAAK,CAACiB,MAAM,CAACS,KAAK;gBACxC,KAAK,SAAS;kBAAE,OAAO1B,KAAK,CAACiB,MAAM,CAACU,IAAI;gBACxC,KAAK,UAAU;kBAAE,OAAO3B,KAAK,CAACiB,MAAM,CAACW,OAAO;gBAC5C;kBAAS,OAAO5B,KAAK,CAACiB,MAAM,CAACY,YAAY;cAC3C;YACF,CAAE;YACFC,SAAS,EAAE,GAAG9B,KAAK,CAACiB,MAAM,CAACc,OAAO;UAAK;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLlF,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE1B,WAAW,CAAC,eACjElC,OAAA,CAACV,WAAW;MACV8C,SAAS,EAAEF,WAAW,CAACE,SAAU;MACjCC,QAAQ,EAAEH,WAAW,CAACG,QAAS;MAC/B4G,YAAY,EAAE/G,WAAW,CAACM,IAAK;MAC/BsI,OAAO,EAAElE,gBAAiB;MAC1BmE,YAAY,EAAElE,gBAAiB;MAC/BmE,QAAQ,EAAElE,YAAa;MACvBmE,WAAW,EAAExD,mBAAoB;MACjCyD,QAAQ,EAAEtD;IAAiB;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,eAGF7I,OAAA,CAACR,WAAW;MACVmD,MAAM,EAAEF,WAAW,CAACE,MAAO;MAC3BH,IAAI,EAAEC,WAAW,CAACD,IAAK;MACvBsI,OAAO,EAAEvC,gBAAiB;MAC1BgB,MAAM,EAAElB;IAAiB;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC3I,EAAA,CA/cID,qBAAqB;EAAA,QACVxB,SAAS,EACPC,WAAW,EAERK,YAAY,EACJQ,QAAQ,EAgBhCE,WAAW,EAQXC,SAAS;AAAA;AAAAyL,EAAA,GA7BTlL,qBAAqB;AAid3B,MAAMmL,cAAc,GAAGA,CAAA,KAAM;EAC3B,oBACEpL,OAAA,CAAChB,iBAAiB;IAAAyJ,QAAA,eAChBzI,OAAA,CAACC,qBAAqB;MAAAyI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAExB,CAAC;AAACwC,GAAA,GANID,cAAc;AAQpB,eAAeA,cAAc;AAAC,IAAAD,EAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}