{"ast": null, "code": "// Utility functions for workflow branching and node management\n\nexport const createNewBranch = (nodes, edges, startNodeId) => {\n  var _startNode$data2;\n  console.log('🔧 createNewBranch called with:', {\n    startNodeId,\n    nodesCount: nodes.length,\n    edgesCount: edges.length\n  });\n\n  // Define the standard VLSI flow sequence\n  const vlsiFlowSequence = [{\n    type: 'floorplan',\n    label: 'Floorplan'\n  }, {\n    type: 'place',\n    label: 'Place'\n  }, {\n    type: 'cts',\n    label: 'CTS'\n  }, {\n    type: 'route',\n    label: 'Route'\n  }];\n\n  // Find the starting node\n  const startNode = nodes.find(node => node.id === startNodeId);\n  console.log('🔧 Found startNode:', startNode);\n  if (!startNode) {\n    console.log('❌ No start node found');\n    return {\n      newNodes: [],\n      newEdges: []\n    };\n  }\n\n  // Determine the starting point in the sequence\n  let startIndex = vlsiFlowSequence.findIndex(step => {\n    var _startNode$type, _startNode$data, _startNode$data$label, _startNode$id;\n    const nodeType = (_startNode$type = startNode.type) === null || _startNode$type === void 0 ? void 0 : _startNode$type.toLowerCase();\n    const nodeLabel = (_startNode$data = startNode.data) === null || _startNode$data === void 0 ? void 0 : (_startNode$data$label = _startNode$data.label) === null || _startNode$data$label === void 0 ? void 0 : _startNode$data$label.toLowerCase();\n    const nodeId = (_startNode$id = startNode.id) === null || _startNode$id === void 0 ? void 0 : _startNode$id.toLowerCase();\n    return nodeType === step.type || nodeLabel === step.type || nodeLabel === step.label.toLowerCase() || nodeId.includes(step.type);\n  });\n  console.log('🔧 Node matching:', {\n    nodeType: startNode.type,\n    nodeLabel: (_startNode$data2 = startNode.data) === null || _startNode$data2 === void 0 ? void 0 : _startNode$data2.label,\n    nodeId: startNode.id,\n    startIndex\n  });\n\n  // If not found in standard sequence, treat as custom node\n  if (startIndex === -1) {\n    console.log('⚠️ Node not found in VLSI sequence, starting from floorplan');\n    startIndex = 0; // Start from floorplan\n  }\n\n  // Generate branch suffix\n  const branchSuffix = `_branch_${Date.now()}`;\n  const newNodes = [];\n  const newEdges = [];\n\n  // Create new nodes for the branch starting from the NEXT stage after the selected node\n  // This creates a branch that continues from the clicked node with subsequent stages only\n  const branchSteps = vlsiFlowSequence.slice(startIndex + 1); // Start from NEXT stage after clicked node\n  console.log('🔧 Branch steps to create:', branchSteps);\n  let previousNodeId = startNodeId; // Start connecting from the original clicked node\n\n  // Create nodes for all subsequent stages in the branch\n  branchSteps.forEach((step, index) => {\n    const nodeId = `${step.type}${branchSuffix}`;\n    const xOffset = 300; // Horizontal offset for branch\n    const yOffset = 100 + index * 150; // Vertical spacing\n\n    // Calculate position relative to the start node\n    const newNode = {\n      id: nodeId,\n      type: step.type,\n      // Use the actual VLSI node type\n      position: {\n        x: startNode.position.x + xOffset,\n        y: startNode.position.y + yOffset\n      },\n      data: {\n        label: `${step.label} (Branch)`,\n        description: `New branch execution of ${step.label}`,\n        status: 'pending',\n        progress: 0,\n        branch: true,\n        branchId: branchSuffix,\n        parentNode: startNodeId,\n        parameters: getDefaultParameters(step.type)\n      }\n    };\n    newNodes.push(newNode);\n\n    // Create edge from previous node\n    const edgeId = `edge_${previousNodeId}_to_${nodeId}`;\n    const isFirstInBranch = index === 0;\n    newEdges.push({\n      id: edgeId,\n      source: previousNodeId,\n      target: nodeId,\n      type: 'custom',\n      data: {\n        label: isFirstInBranch ? 'New Branch' : 'Branch Flow',\n        color: isFirstInBranch ? '#10b981' : '#3b82f6',\n        dashed: isFirstInBranch\n      }\n    });\n    previousNodeId = nodeId;\n  });\n\n  // If no subsequent steps are found (e.g., clicked on Route node - the last stage)\n  // Create a duplicate of the same stage as a branch\n  if (branchSteps.length === 0) {\n    var _startNode$data3;\n    const currentStep = vlsiFlowSequence[startIndex] || {\n      type: startNode.type,\n      label: ((_startNode$data3 = startNode.data) === null || _startNode$data3 === void 0 ? void 0 : _startNode$data3.label) || 'Node'\n    };\n    const nodeId = `${currentStep.type}${branchSuffix}`;\n    const xOffset = 300;\n    const yOffset = 100;\n    const newNode = {\n      id: nodeId,\n      type: currentStep.type,\n      position: {\n        x: startNode.position.x + xOffset,\n        y: startNode.position.y + yOffset\n      },\n      data: {\n        label: `${currentStep.label} (Branch)`,\n        description: `New branch execution of ${currentStep.label}`,\n        status: 'pending',\n        progress: 0,\n        branch: true,\n        branchId: branchSuffix,\n        parentNode: startNodeId,\n        parameters: getDefaultParameters(currentStep.type)\n      }\n    };\n    newNodes.push(newNode);\n\n    // Connect to the original start node\n    const edgeId = `edge_${startNodeId}_to_${nodeId}`;\n    newEdges.push({\n      id: edgeId,\n      source: startNodeId,\n      target: nodeId,\n      type: 'custom',\n      data: {\n        label: 'New Branch',\n        color: '#10b981',\n        dashed: true\n      }\n    });\n  }\n  console.log('🔧 Final result:', {\n    newNodesCount: newNodes.length,\n    newEdgesCount: newEdges.length\n  });\n  console.log('🔧 New nodes:', newNodes.map(n => ({\n    id: n.id,\n    type: n.type,\n    label: n.data.label\n  })));\n  return {\n    newNodes,\n    newEdges\n  };\n};\nexport const getDefaultParameters = nodeType => {\n  const defaultParams = {\n    floorplan: {\n      'aspect_ratio': '1.0',\n      'utilization': '70%',\n      'power_rings': 'enabled',\n      'io_placement': 'auto',\n      'core_margin': '10um'\n    },\n    place: {\n      'density': '0.8',\n      'timing_driven': 'true',\n      'congestion_driven': 'true',\n      'effort': 'high',\n      'max_displacement': '5um'\n    },\n    cts: {\n      'skew_target': '50ps',\n      'buffer_type': 'CLKBUF',\n      'max_fanout': '16',\n      'insertion_delay': '100ps',\n      'balance_mode': 'top_down'\n    },\n    route: {\n      'layers': '9',\n      'via_optimization': 'enabled',\n      'timing_driven': 'true',\n      'crosstalk_prevention': 'enabled',\n      'detail_route_effort': 'high'\n    }\n  };\n  return defaultParams[nodeType] || {};\n};\nexport const duplicateNode = (node, nodes) => {\n  var _node$data;\n  const duplicateId = `${node.id}_copy_${Date.now()}`;\n  const offset = 50;\n  return {\n    ...node,\n    id: duplicateId,\n    position: {\n      x: node.position.x + offset,\n      y: node.position.y + offset\n    },\n    data: {\n      ...node.data,\n      label: `${((_node$data = node.data) === null || _node$data === void 0 ? void 0 : _node$data.label) || node.id} (Copy)`,\n      status: 'pending',\n      progress: 0\n    }\n  };\n};\nexport const getNodesByType = (nodes, nodeType) => {\n  return nodes.filter(node => {\n    var _node$data2, _node$data2$label;\n    return node.type === nodeType || ((_node$data2 = node.data) === null || _node$data2 === void 0 ? void 0 : (_node$data2$label = _node$data2.label) === null || _node$data2$label === void 0 ? void 0 : _node$data2$label.toLowerCase().includes(nodeType)) || node.id.toLowerCase().includes(nodeType);\n  });\n};\nexport const findDownstreamNodes = (nodeId, edges, nodes) => {\n  const downstreamNodeIds = new Set();\n  const visited = new Set();\n  const traverse = currentNodeId => {\n    if (visited.has(currentNodeId)) return;\n    visited.add(currentNodeId);\n    const outgoingEdges = edges.filter(edge => edge.source === currentNodeId);\n    outgoingEdges.forEach(edge => {\n      downstreamNodeIds.add(edge.target);\n      traverse(edge.target);\n    });\n  };\n  traverse(nodeId);\n  return nodes.filter(node => downstreamNodeIds.has(node.id));\n};\nexport const validateBranchCreation = (startNode, nodes, edges) => {\n  var _startNode$data4;\n  const errors = [];\n  if (!startNode) {\n    errors.push('No start node specified');\n    return {\n      isValid: false,\n      errors\n    };\n  }\n\n  // Check if node is already running\n  if (((_startNode$data4 = startNode.data) === null || _startNode$data4 === void 0 ? void 0 : _startNode$data4.status) === 'running') {\n    errors.push('Cannot create branch from running node');\n  }\n\n  // Check if there are already too many branches\n  const existingBranches = nodes.filter(node => {\n    var _node$data3, _node$data4;\n    return ((_node$data3 = node.data) === null || _node$data3 === void 0 ? void 0 : _node$data3.branch) && ((_node$data4 = node.data) === null || _node$data4 === void 0 ? void 0 : _node$data4.parentNode) === startNode.id;\n  });\n  if (existingBranches.length >= 3) {\n    errors.push('Maximum number of branches (3) already exists for this node');\n  }\n  return {\n    isValid: errors.length === 0,\n    errors\n  };\n};\nexport const getBranchInfo = node => {\n  var _node$data5;\n  if (!((_node$data5 = node.data) !== null && _node$data5 !== void 0 && _node$data5.branch)) {\n    return null;\n  }\n  return {\n    branchId: node.data.branchId,\n    parentNode: node.data.parentNode,\n    isBranch: true\n  };\n};\nexport const getNodeExecutionOrder = nodeType => {\n  const executionOrder = {\n    'floorplan': 1,\n    'place': 2,\n    'cts': 3,\n    'route': 4\n  };\n  return executionOrder[nodeType] || 0;\n};", "map": {"version": 3, "names": ["createNewBranch", "nodes", "edges", "startNodeId", "_startNode$data2", "console", "log", "nodesCount", "length", "edgesCount", "vlsiFlowSequence", "type", "label", "startNode", "find", "node", "id", "newNodes", "newEdges", "startIndex", "findIndex", "step", "_startNode$type", "_startNode$data", "_startNode$data$label", "_startNode$id", "nodeType", "toLowerCase", "nodeLabel", "data", "nodeId", "includes", "branchSuffix", "Date", "now", "branchSteps", "slice", "previousNodeId", "for<PERSON>ach", "index", "xOffset", "yOffset", "newNode", "position", "x", "y", "description", "status", "progress", "branch", "branchId", "parentNode", "parameters", "getDefaultParameters", "push", "edgeId", "isFirstInBranch", "source", "target", "color", "dashed", "_startNode$data3", "currentStep", "newNodesCount", "newEdgesCount", "map", "n", "defaultParams", "floorplan", "place", "cts", "route", "duplicateNode", "_node$data", "duplicateId", "offset", "getNodesByType", "filter", "_node$data2", "_node$data2$label", "findDownstreamNodes", "downstreamNodeIds", "Set", "visited", "traverse", "currentNodeId", "has", "add", "outgoing<PERSON><PERSON>", "edge", "validateBranchCreation", "_startNode$data4", "errors", "<PERSON><PERSON><PERSON><PERSON>", "existingBranches", "_node$data3", "_node$data4", "getBranchInfo", "_node$data5", "isBranch", "getNodeExecutionOrder", "executionOrder"], "sources": ["/home/<USER>/vlsi-workflow/client/src/utils/workflowBranching.js"], "sourcesContent": ["// Utility functions for workflow branching and node management\n\nexport const createNewBranch = (nodes, edges, startNodeId) => {\n  console.log('🔧 createNewBranch called with:', { startNodeId, nodesCount: nodes.length, edgesCount: edges.length });\n  \n  // Define the standard VLSI flow sequence\n  const vlsiFlowSequence = [\n    { type: 'floorplan', label: 'Floorplan' },\n    { type: 'place', label: 'Place' },\n    { type: 'cts', label: 'CTS' },\n    { type: 'route', label: 'Route' }\n  ];\n\n  // Find the starting node\n  const startNode = nodes.find(node => node.id === startNodeId);\n  console.log('🔧 Found startNode:', startNode);\n  if (!startNode) {\n    console.log('❌ No start node found');\n    return { newNodes: [], newEdges: [] };\n  }\n\n  // Determine the starting point in the sequence\n  let startIndex = vlsiFlowSequence.findIndex(step => {\n    const nodeType = startNode.type?.toLowerCase();\n    const nodeLabel = startNode.data?.label?.toLowerCase();\n    const nodeId = startNode.id?.toLowerCase();\n\n    return nodeType === step.type ||\n           nodeLabel === step.type ||\n           nodeLabel === step.label.toLowerCase() ||\n           nodeId.includes(step.type);\n  });\n\n  console.log('🔧 Node matching:', {\n    nodeType: startNode.type,\n    nodeLabel: startNode.data?.label,\n    nodeId: startNode.id,\n    startIndex\n  });\n\n  // If not found in standard sequence, treat as custom node\n  if (startIndex === -1) {\n    console.log('⚠️ Node not found in VLSI sequence, starting from floorplan');\n    startIndex = 0; // Start from floorplan\n  }\n\n  // Generate branch suffix\n  const branchSuffix = `_branch_${Date.now()}`;\n  const newNodes = [];\n  const newEdges = [];\n\n  // Create new nodes for the branch starting from the NEXT stage after the selected node\n  // This creates a branch that continues from the clicked node with subsequent stages only\n  const branchSteps = vlsiFlowSequence.slice(startIndex + 1); // Start from NEXT stage after clicked node\n  console.log('🔧 Branch steps to create:', branchSteps);\n  let previousNodeId = startNodeId; // Start connecting from the original clicked node\n\n  // Create nodes for all subsequent stages in the branch\n  branchSteps.forEach((step, index) => {\n    const nodeId = `${step.type}${branchSuffix}`;\n    const xOffset = 300; // Horizontal offset for branch\n    const yOffset = 100 + (index * 150); // Vertical spacing\n\n    // Calculate position relative to the start node\n    const newNode = {\n      id: nodeId,\n      type: step.type, // Use the actual VLSI node type\n      position: {\n        x: startNode.position.x + xOffset,\n        y: startNode.position.y + yOffset\n      },\n      data: {\n        label: `${step.label} (Branch)`,\n        description: `New branch execution of ${step.label}`,\n        status: 'pending',\n        progress: 0,\n        branch: true,\n        branchId: branchSuffix,\n        parentNode: startNodeId,\n        parameters: getDefaultParameters(step.type)\n      }\n    };\n\n    newNodes.push(newNode);\n\n    // Create edge from previous node\n    const edgeId = `edge_${previousNodeId}_to_${nodeId}`;\n    const isFirstInBranch = index === 0;\n\n    newEdges.push({\n      id: edgeId,\n      source: previousNodeId,\n      target: nodeId,\n      type: 'custom',\n      data: {\n        label: isFirstInBranch ? 'New Branch' : 'Branch Flow',\n        color: isFirstInBranch ? '#10b981' : '#3b82f6',\n        dashed: isFirstInBranch\n      }\n    });\n\n    previousNodeId = nodeId;\n  });\n\n  // If no subsequent steps are found (e.g., clicked on Route node - the last stage)\n  // Create a duplicate of the same stage as a branch\n  if (branchSteps.length === 0) {\n    const currentStep = vlsiFlowSequence[startIndex] || { type: startNode.type, label: startNode.data?.label || 'Node' };\n    const nodeId = `${currentStep.type}${branchSuffix}`;\n    const xOffset = 300;\n    const yOffset = 100;\n\n    const newNode = {\n      id: nodeId,\n      type: currentStep.type,\n      position: {\n        x: startNode.position.x + xOffset,\n        y: startNode.position.y + yOffset\n      },\n      data: {\n        label: `${currentStep.label} (Branch)`,\n        description: `New branch execution of ${currentStep.label}`,\n        status: 'pending',\n        progress: 0,\n        branch: true,\n        branchId: branchSuffix,\n        parentNode: startNodeId,\n        parameters: getDefaultParameters(currentStep.type)\n      }\n    };\n\n    newNodes.push(newNode);\n\n    // Connect to the original start node\n    const edgeId = `edge_${startNodeId}_to_${nodeId}`;\n    newEdges.push({\n      id: edgeId,\n      source: startNodeId,\n      target: nodeId,\n      type: 'custom',\n      data: {\n        label: 'New Branch',\n        color: '#10b981',\n        dashed: true\n      }\n    });\n  }\n\n  console.log('🔧 Final result:', { newNodesCount: newNodes.length, newEdgesCount: newEdges.length });\n  console.log('🔧 New nodes:', newNodes.map(n => ({ id: n.id, type: n.type, label: n.data.label })));\n  \n  return { newNodes, newEdges };\n};\n\nexport const getDefaultParameters = (nodeType) => {\n  const defaultParams = {\n    floorplan: {\n      'aspect_ratio': '1.0',\n      'utilization': '70%',\n      'power_rings': 'enabled',\n      'io_placement': 'auto',\n      'core_margin': '10um'\n    },\n    place: {\n      'density': '0.8',\n      'timing_driven': 'true',\n      'congestion_driven': 'true',\n      'effort': 'high',\n      'max_displacement': '5um'\n    },\n    cts: {\n      'skew_target': '50ps',\n      'buffer_type': 'CLKBUF',\n      'max_fanout': '16',\n      'insertion_delay': '100ps',\n      'balance_mode': 'top_down'\n    },\n    route: {\n      'layers': '9',\n      'via_optimization': 'enabled',\n      'timing_driven': 'true',\n      'crosstalk_prevention': 'enabled',\n      'detail_route_effort': 'high'\n    }\n  };\n\n  return defaultParams[nodeType] || {};\n};\n\nexport const duplicateNode = (node, nodes) => {\n  const duplicateId = `${node.id}_copy_${Date.now()}`;\n  const offset = 50;\n\n  return {\n    ...node,\n    id: duplicateId,\n    position: {\n      x: node.position.x + offset,\n      y: node.position.y + offset\n    },\n    data: {\n      ...node.data,\n      label: `${node.data?.label || node.id} (Copy)`,\n      status: 'pending',\n      progress: 0\n    }\n  };\n};\n\nexport const getNodesByType = (nodes, nodeType) => {\n  return nodes.filter(node => \n    node.type === nodeType || \n    node.data?.label?.toLowerCase().includes(nodeType) ||\n    node.id.toLowerCase().includes(nodeType)\n  );\n};\n\nexport const findDownstreamNodes = (nodeId, edges, nodes) => {\n  const downstreamNodeIds = new Set();\n  const visited = new Set();\n\n  const traverse = (currentNodeId) => {\n    if (visited.has(currentNodeId)) return;\n    visited.add(currentNodeId);\n\n    const outgoingEdges = edges.filter(edge => edge.source === currentNodeId);\n    outgoingEdges.forEach(edge => {\n      downstreamNodeIds.add(edge.target);\n      traverse(edge.target);\n    });\n  };\n\n  traverse(nodeId);\n  return nodes.filter(node => downstreamNodeIds.has(node.id));\n};\n\nexport const validateBranchCreation = (startNode, nodes, edges) => {\n  const errors = [];\n\n  if (!startNode) {\n    errors.push('No start node specified');\n    return { isValid: false, errors };\n  }\n\n  // Check if node is already running\n  if (startNode.data?.status === 'running') {\n    errors.push('Cannot create branch from running node');\n  }\n\n  // Check if there are already too many branches\n  const existingBranches = nodes.filter(node =>\n    node.data?.branch && node.data?.parentNode === startNode.id\n  );\n\n  if (existingBranches.length >= 3) {\n    errors.push('Maximum number of branches (3) already exists for this node');\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors\n  };\n};\n\nexport const getBranchInfo = (node) => {\n  if (!node.data?.branch) {\n    return null;\n  }\n\n  return {\n    branchId: node.data.branchId,\n    parentNode: node.data.parentNode,\n    isBranch: true\n  };\n};\n\nexport const getNodeExecutionOrder = (nodeType) => {\n  const executionOrder = {\n    'floorplan': 1,\n    'place': 2,\n    'cts': 3,\n    'route': 4\n  };\n\n  return executionOrder[nodeType] || 0;\n};"], "mappings": "AAAA;;AAEA,OAAO,MAAMA,eAAe,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,WAAW,KAAK;EAAA,IAAAC,gBAAA;EAC5DC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;IAAEH,WAAW;IAAEI,UAAU,EAAEN,KAAK,CAACO,MAAM;IAAEC,UAAU,EAAEP,KAAK,CAACM;EAAO,CAAC,CAAC;;EAEnH;EACA,MAAME,gBAAgB,GAAG,CACvB;IAAEC,IAAI,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAY,CAAC,EACzC;IAAED,IAAI,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAQ,CAAC,EACjC;IAAED,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAM,CAAC,EAC7B;IAAED,IAAI,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAQ,CAAC,CAClC;;EAED;EACA,MAAMC,SAAS,GAAGZ,KAAK,CAACa,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAKb,WAAW,CAAC;EAC7DE,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEO,SAAS,CAAC;EAC7C,IAAI,CAACA,SAAS,EAAE;IACdR,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;IACpC,OAAO;MAAEW,QAAQ,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAG,CAAC;EACvC;;EAEA;EACA,IAAIC,UAAU,GAAGT,gBAAgB,CAACU,SAAS,CAACC,IAAI,IAAI;IAAA,IAAAC,eAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,aAAA;IAClD,MAAMC,QAAQ,IAAAJ,eAAA,GAAGT,SAAS,CAACF,IAAI,cAAAW,eAAA,uBAAdA,eAAA,CAAgBK,WAAW,CAAC,CAAC;IAC9C,MAAMC,SAAS,IAAAL,eAAA,GAAGV,SAAS,CAACgB,IAAI,cAAAN,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBX,KAAK,cAAAY,qBAAA,uBAArBA,qBAAA,CAAuBG,WAAW,CAAC,CAAC;IACtD,MAAMG,MAAM,IAAAL,aAAA,GAAGZ,SAAS,CAACG,EAAE,cAAAS,aAAA,uBAAZA,aAAA,CAAcE,WAAW,CAAC,CAAC;IAE1C,OAAOD,QAAQ,KAAKL,IAAI,CAACV,IAAI,IACtBiB,SAAS,KAAKP,IAAI,CAACV,IAAI,IACvBiB,SAAS,KAAKP,IAAI,CAACT,KAAK,CAACe,WAAW,CAAC,CAAC,IACtCG,MAAM,CAACC,QAAQ,CAACV,IAAI,CAACV,IAAI,CAAC;EACnC,CAAC,CAAC;EAEFN,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE;IAC/BoB,QAAQ,EAAEb,SAAS,CAACF,IAAI;IACxBiB,SAAS,GAAAxB,gBAAA,GAAES,SAAS,CAACgB,IAAI,cAAAzB,gBAAA,uBAAdA,gBAAA,CAAgBQ,KAAK;IAChCkB,MAAM,EAAEjB,SAAS,CAACG,EAAE;IACpBG;EACF,CAAC,CAAC;;EAEF;EACA,IAAIA,UAAU,KAAK,CAAC,CAAC,EAAE;IACrBd,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;IAC1Ea,UAAU,GAAG,CAAC,CAAC,CAAC;EAClB;;EAEA;EACA,MAAMa,YAAY,GAAG,WAAWC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;EAC5C,MAAMjB,QAAQ,GAAG,EAAE;EACnB,MAAMC,QAAQ,GAAG,EAAE;;EAEnB;EACA;EACA,MAAMiB,WAAW,GAAGzB,gBAAgB,CAAC0B,KAAK,CAACjB,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;EAC5Dd,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE6B,WAAW,CAAC;EACtD,IAAIE,cAAc,GAAGlC,WAAW,CAAC,CAAC;;EAElC;EACAgC,WAAW,CAACG,OAAO,CAAC,CAACjB,IAAI,EAAEkB,KAAK,KAAK;IACnC,MAAMT,MAAM,GAAG,GAAGT,IAAI,CAACV,IAAI,GAAGqB,YAAY,EAAE;IAC5C,MAAMQ,OAAO,GAAG,GAAG,CAAC,CAAC;IACrB,MAAMC,OAAO,GAAG,GAAG,GAAIF,KAAK,GAAG,GAAI,CAAC,CAAC;;IAErC;IACA,MAAMG,OAAO,GAAG;MACd1B,EAAE,EAAEc,MAAM;MACVnB,IAAI,EAAEU,IAAI,CAACV,IAAI;MAAE;MACjBgC,QAAQ,EAAE;QACRC,CAAC,EAAE/B,SAAS,CAAC8B,QAAQ,CAACC,CAAC,GAAGJ,OAAO;QACjCK,CAAC,EAAEhC,SAAS,CAAC8B,QAAQ,CAACE,CAAC,GAAGJ;MAC5B,CAAC;MACDZ,IAAI,EAAE;QACJjB,KAAK,EAAE,GAAGS,IAAI,CAACT,KAAK,WAAW;QAC/BkC,WAAW,EAAE,2BAA2BzB,IAAI,CAACT,KAAK,EAAE;QACpDmC,MAAM,EAAE,SAAS;QACjBC,QAAQ,EAAE,CAAC;QACXC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAElB,YAAY;QACtBmB,UAAU,EAAEhD,WAAW;QACvBiD,UAAU,EAAEC,oBAAoB,CAAChC,IAAI,CAACV,IAAI;MAC5C;IACF,CAAC;IAEDM,QAAQ,CAACqC,IAAI,CAACZ,OAAO,CAAC;;IAEtB;IACA,MAAMa,MAAM,GAAG,QAAQlB,cAAc,OAAOP,MAAM,EAAE;IACpD,MAAM0B,eAAe,GAAGjB,KAAK,KAAK,CAAC;IAEnCrB,QAAQ,CAACoC,IAAI,CAAC;MACZtC,EAAE,EAAEuC,MAAM;MACVE,MAAM,EAAEpB,cAAc;MACtBqB,MAAM,EAAE5B,MAAM;MACdnB,IAAI,EAAE,QAAQ;MACdkB,IAAI,EAAE;QACJjB,KAAK,EAAE4C,eAAe,GAAG,YAAY,GAAG,aAAa;QACrDG,KAAK,EAAEH,eAAe,GAAG,SAAS,GAAG,SAAS;QAC9CI,MAAM,EAAEJ;MACV;IACF,CAAC,CAAC;IAEFnB,cAAc,GAAGP,MAAM;EACzB,CAAC,CAAC;;EAEF;EACA;EACA,IAAIK,WAAW,CAAC3B,MAAM,KAAK,CAAC,EAAE;IAAA,IAAAqD,gBAAA;IAC5B,MAAMC,WAAW,GAAGpD,gBAAgB,CAACS,UAAU,CAAC,IAAI;MAAER,IAAI,EAAEE,SAAS,CAACF,IAAI;MAAEC,KAAK,EAAE,EAAAiD,gBAAA,GAAAhD,SAAS,CAACgB,IAAI,cAAAgC,gBAAA,uBAAdA,gBAAA,CAAgBjD,KAAK,KAAI;IAAO,CAAC;IACpH,MAAMkB,MAAM,GAAG,GAAGgC,WAAW,CAACnD,IAAI,GAAGqB,YAAY,EAAE;IACnD,MAAMQ,OAAO,GAAG,GAAG;IACnB,MAAMC,OAAO,GAAG,GAAG;IAEnB,MAAMC,OAAO,GAAG;MACd1B,EAAE,EAAEc,MAAM;MACVnB,IAAI,EAAEmD,WAAW,CAACnD,IAAI;MACtBgC,QAAQ,EAAE;QACRC,CAAC,EAAE/B,SAAS,CAAC8B,QAAQ,CAACC,CAAC,GAAGJ,OAAO;QACjCK,CAAC,EAAEhC,SAAS,CAAC8B,QAAQ,CAACE,CAAC,GAAGJ;MAC5B,CAAC;MACDZ,IAAI,EAAE;QACJjB,KAAK,EAAE,GAAGkD,WAAW,CAAClD,KAAK,WAAW;QACtCkC,WAAW,EAAE,2BAA2BgB,WAAW,CAAClD,KAAK,EAAE;QAC3DmC,MAAM,EAAE,SAAS;QACjBC,QAAQ,EAAE,CAAC;QACXC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAElB,YAAY;QACtBmB,UAAU,EAAEhD,WAAW;QACvBiD,UAAU,EAAEC,oBAAoB,CAACS,WAAW,CAACnD,IAAI;MACnD;IACF,CAAC;IAEDM,QAAQ,CAACqC,IAAI,CAACZ,OAAO,CAAC;;IAEtB;IACA,MAAMa,MAAM,GAAG,QAAQpD,WAAW,OAAO2B,MAAM,EAAE;IACjDZ,QAAQ,CAACoC,IAAI,CAAC;MACZtC,EAAE,EAAEuC,MAAM;MACVE,MAAM,EAAEtD,WAAW;MACnBuD,MAAM,EAAE5B,MAAM;MACdnB,IAAI,EAAE,QAAQ;MACdkB,IAAI,EAAE;QACJjB,KAAK,EAAE,YAAY;QACnB+C,KAAK,EAAE,SAAS;QAChBC,MAAM,EAAE;MACV;IACF,CAAC,CAAC;EACJ;EAEAvD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE;IAAEyD,aAAa,EAAE9C,QAAQ,CAACT,MAAM;IAAEwD,aAAa,EAAE9C,QAAQ,CAACV;EAAO,CAAC,CAAC;EACnGH,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEW,QAAQ,CAACgD,GAAG,CAACC,CAAC,KAAK;IAAElD,EAAE,EAAEkD,CAAC,CAAClD,EAAE;IAAEL,IAAI,EAAEuD,CAAC,CAACvD,IAAI;IAAEC,KAAK,EAAEsD,CAAC,CAACrC,IAAI,CAACjB;EAAM,CAAC,CAAC,CAAC,CAAC;EAElG,OAAO;IAAEK,QAAQ;IAAEC;EAAS,CAAC;AAC/B,CAAC;AAED,OAAO,MAAMmC,oBAAoB,GAAI3B,QAAQ,IAAK;EAChD,MAAMyC,aAAa,GAAG;IACpBC,SAAS,EAAE;MACT,cAAc,EAAE,KAAK;MACrB,aAAa,EAAE,KAAK;MACpB,aAAa,EAAE,SAAS;MACxB,cAAc,EAAE,MAAM;MACtB,aAAa,EAAE;IACjB,CAAC;IACDC,KAAK,EAAE;MACL,SAAS,EAAE,KAAK;MAChB,eAAe,EAAE,MAAM;MACvB,mBAAmB,EAAE,MAAM;MAC3B,QAAQ,EAAE,MAAM;MAChB,kBAAkB,EAAE;IACtB,CAAC;IACDC,GAAG,EAAE;MACH,aAAa,EAAE,MAAM;MACrB,aAAa,EAAE,QAAQ;MACvB,YAAY,EAAE,IAAI;MAClB,iBAAiB,EAAE,OAAO;MAC1B,cAAc,EAAE;IAClB,CAAC;IACDC,KAAK,EAAE;MACL,QAAQ,EAAE,GAAG;MACb,kBAAkB,EAAE,SAAS;MAC7B,eAAe,EAAE,MAAM;MACvB,sBAAsB,EAAE,SAAS;MACjC,qBAAqB,EAAE;IACzB;EACF,CAAC;EAED,OAAOJ,aAAa,CAACzC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACtC,CAAC;AAED,OAAO,MAAM8C,aAAa,GAAGA,CAACzD,IAAI,EAAEd,KAAK,KAAK;EAAA,IAAAwE,UAAA;EAC5C,MAAMC,WAAW,GAAG,GAAG3D,IAAI,CAACC,EAAE,SAASiB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;EACnD,MAAMyC,MAAM,GAAG,EAAE;EAEjB,OAAO;IACL,GAAG5D,IAAI;IACPC,EAAE,EAAE0D,WAAW;IACf/B,QAAQ,EAAE;MACRC,CAAC,EAAE7B,IAAI,CAAC4B,QAAQ,CAACC,CAAC,GAAG+B,MAAM;MAC3B9B,CAAC,EAAE9B,IAAI,CAAC4B,QAAQ,CAACE,CAAC,GAAG8B;IACvB,CAAC;IACD9C,IAAI,EAAE;MACJ,GAAGd,IAAI,CAACc,IAAI;MACZjB,KAAK,EAAE,GAAG,EAAA6D,UAAA,GAAA1D,IAAI,CAACc,IAAI,cAAA4C,UAAA,uBAATA,UAAA,CAAW7D,KAAK,KAAIG,IAAI,CAACC,EAAE,SAAS;MAC9C+B,MAAM,EAAE,SAAS;MACjBC,QAAQ,EAAE;IACZ;EACF,CAAC;AACH,CAAC;AAED,OAAO,MAAM4B,cAAc,GAAGA,CAAC3E,KAAK,EAAEyB,QAAQ,KAAK;EACjD,OAAOzB,KAAK,CAAC4E,MAAM,CAAC9D,IAAI;IAAA,IAAA+D,WAAA,EAAAC,iBAAA;IAAA,OACtBhE,IAAI,CAACJ,IAAI,KAAKe,QAAQ,MAAAoD,WAAA,GACtB/D,IAAI,CAACc,IAAI,cAAAiD,WAAA,wBAAAC,iBAAA,GAATD,WAAA,CAAWlE,KAAK,cAAAmE,iBAAA,uBAAhBA,iBAAA,CAAkBpD,WAAW,CAAC,CAAC,CAACI,QAAQ,CAACL,QAAQ,CAAC,KAClDX,IAAI,CAACC,EAAE,CAACW,WAAW,CAAC,CAAC,CAACI,QAAQ,CAACL,QAAQ,CAAC;EAAA,CAC1C,CAAC;AACH,CAAC;AAED,OAAO,MAAMsD,mBAAmB,GAAGA,CAAClD,MAAM,EAAE5B,KAAK,EAAED,KAAK,KAAK;EAC3D,MAAMgF,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAAC;EACnC,MAAMC,OAAO,GAAG,IAAID,GAAG,CAAC,CAAC;EAEzB,MAAME,QAAQ,GAAIC,aAAa,IAAK;IAClC,IAAIF,OAAO,CAACG,GAAG,CAACD,aAAa,CAAC,EAAE;IAChCF,OAAO,CAACI,GAAG,CAACF,aAAa,CAAC;IAE1B,MAAMG,aAAa,GAAGtF,KAAK,CAAC2E,MAAM,CAACY,IAAI,IAAIA,IAAI,CAAChC,MAAM,KAAK4B,aAAa,CAAC;IACzEG,aAAa,CAAClD,OAAO,CAACmD,IAAI,IAAI;MAC5BR,iBAAiB,CAACM,GAAG,CAACE,IAAI,CAAC/B,MAAM,CAAC;MAClC0B,QAAQ,CAACK,IAAI,CAAC/B,MAAM,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC;EAED0B,QAAQ,CAACtD,MAAM,CAAC;EAChB,OAAO7B,KAAK,CAAC4E,MAAM,CAAC9D,IAAI,IAAIkE,iBAAiB,CAACK,GAAG,CAACvE,IAAI,CAACC,EAAE,CAAC,CAAC;AAC7D,CAAC;AAED,OAAO,MAAM0E,sBAAsB,GAAGA,CAAC7E,SAAS,EAAEZ,KAAK,EAAEC,KAAK,KAAK;EAAA,IAAAyF,gBAAA;EACjE,MAAMC,MAAM,GAAG,EAAE;EAEjB,IAAI,CAAC/E,SAAS,EAAE;IACd+E,MAAM,CAACtC,IAAI,CAAC,yBAAyB,CAAC;IACtC,OAAO;MAAEuC,OAAO,EAAE,KAAK;MAAED;IAAO,CAAC;EACnC;;EAEA;EACA,IAAI,EAAAD,gBAAA,GAAA9E,SAAS,CAACgB,IAAI,cAAA8D,gBAAA,uBAAdA,gBAAA,CAAgB5C,MAAM,MAAK,SAAS,EAAE;IACxC6C,MAAM,CAACtC,IAAI,CAAC,wCAAwC,CAAC;EACvD;;EAEA;EACA,MAAMwC,gBAAgB,GAAG7F,KAAK,CAAC4E,MAAM,CAAC9D,IAAI;IAAA,IAAAgF,WAAA,EAAAC,WAAA;IAAA,OACxC,EAAAD,WAAA,GAAAhF,IAAI,CAACc,IAAI,cAAAkE,WAAA,uBAATA,WAAA,CAAW9C,MAAM,KAAI,EAAA+C,WAAA,GAAAjF,IAAI,CAACc,IAAI,cAAAmE,WAAA,uBAATA,WAAA,CAAW7C,UAAU,MAAKtC,SAAS,CAACG,EAAE;EAAA,CAC7D,CAAC;EAED,IAAI8E,gBAAgB,CAACtF,MAAM,IAAI,CAAC,EAAE;IAChCoF,MAAM,CAACtC,IAAI,CAAC,6DAA6D,CAAC;EAC5E;EAEA,OAAO;IACLuC,OAAO,EAAED,MAAM,CAACpF,MAAM,KAAK,CAAC;IAC5BoF;EACF,CAAC;AACH,CAAC;AAED,OAAO,MAAMK,aAAa,GAAIlF,IAAI,IAAK;EAAA,IAAAmF,WAAA;EACrC,IAAI,GAAAA,WAAA,GAACnF,IAAI,CAACc,IAAI,cAAAqE,WAAA,eAATA,WAAA,CAAWjD,MAAM,GAAE;IACtB,OAAO,IAAI;EACb;EAEA,OAAO;IACLC,QAAQ,EAAEnC,IAAI,CAACc,IAAI,CAACqB,QAAQ;IAC5BC,UAAU,EAAEpC,IAAI,CAACc,IAAI,CAACsB,UAAU;IAChCgD,QAAQ,EAAE;EACZ,CAAC;AACH,CAAC;AAED,OAAO,MAAMC,qBAAqB,GAAI1E,QAAQ,IAAK;EACjD,MAAM2E,cAAc,GAAG;IACrB,WAAW,EAAE,CAAC;IACd,OAAO,EAAE,CAAC;IACV,KAAK,EAAE,CAAC;IACR,OAAO,EAAE;EACX,CAAC;EAED,OAAOA,cAAc,CAAC3E,QAAQ,CAAC,IAAI,CAAC;AACtC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}