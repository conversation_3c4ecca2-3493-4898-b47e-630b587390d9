[{"/home/<USER>/vlsi-workflow/client/src/index.js": "1", "/home/<USER>/vlsi-workflow/client/src/App.js": "2", "/home/<USER>/vlsi-workflow/client/src/contexts/WorkflowContext.js": "3", "/home/<USER>/vlsi-workflow/client/src/components/Dashboard.js": "4", "/home/<USER>/vlsi-workflow/client/src/components/WorkflowEditor.js": "5", "/home/<USER>/vlsi-workflow/client/src/contexts/SocketContext.js": "6", "/home/<USER>/vlsi-workflow/client/src/components/Toolbar.js": "7", "/home/<USER>/vlsi-workflow/client/src/services/api.js": "8", "/home/<USER>/vlsi-workflow/client/src/components/sidebar/Sidebar.js": "9", "/home/<USER>/vlsi-workflow/client/src/components/edges/index.js": "10", "/home/<USER>/vlsi-workflow/client/src/components/nodes/index.js": "11", "/home/<USER>/vlsi-workflow/client/src/components/sidebar/CollaborationPanel.js": "12", "/home/<USER>/vlsi-workflow/client/src/components/sidebar/NodePalette.js": "13", "/home/<USER>/vlsi-workflow/client/src/components/sidebar/LayersPanel.js": "14", "/home/<USER>/vlsi-workflow/client/src/components/sidebar/PropertyPanel.js": "15", "/home/<USER>/vlsi-workflow/client/src/components/nodes/OutputNode.js": "16", "/home/<USER>/vlsi-workflow/client/src/components/edges/CustomEdge.js": "17", "/home/<USER>/vlsi-workflow/client/src/components/nodes/DecisionNode.js": "18", "/home/<USER>/vlsi-workflow/client/src/components/nodes/ProcessNode.js": "19", "/home/<USER>/vlsi-workflow/client/src/components/nodes/InputNode.js": "20", "/home/<USER>/vlsi-workflow/client/src/components/nodes/CustomNode.js": "21", "/home/<USER>/vlsi-workflow/client/src/components/ConfigModal.js": "22", "/home/<USER>/vlsi-workflow/client/src/components/ContextMenu.js": "23", "/home/<USER>/vlsi-workflow/client/src/utils/workflowBranching.js": "24", "/home/<USER>/vlsi-workflow/client/src/contexts/ThemeContext.js": "25", "/home/<USER>/vlsi-workflow/client/src/components/ThemeSwitcher.js": "26", "/home/<USER>/vlsi-workflow/client/src/components/nodes/CTSNode.js": "27", "/home/<USER>/vlsi-workflow/client/src/components/nodes/FloorplanNode.js": "28", "/home/<USER>/vlsi-workflow/client/src/components/nodes/PlaceNode.js": "29", "/home/<USER>/vlsi-workflow/client/src/components/nodes/RouteNode.js": "30"}, {"size": 253, "mtime": 1753947419252, "results": "31", "hashOfConfig": "32"}, {"size": 1728, "mtime": 1753950598682, "results": "33", "hashOfConfig": "32"}, {"size": 5831, "mtime": 1753947476198, "results": "34", "hashOfConfig": "32"}, {"size": 14693, "mtime": 1753954416385, "results": "35", "hashOfConfig": "32"}, {"size": 14246, "mtime": 1754026260619, "results": "36", "hashOfConfig": "32"}, {"size": 2113, "mtime": 1753948150400, "results": "37", "hashOfConfig": "32"}, {"size": 7661, "mtime": 1753950778883, "results": "38", "hashOfConfig": "32"}, {"size": 4012, "mtime": 1753948150400, "results": "39", "hashOfConfig": "32"}, {"size": 4134, "mtime": 1753950918606, "results": "40", "hashOfConfig": "32"}, {"size": 115, "mtime": 1753947576674, "results": "41", "hashOfConfig": "32"}, {"size": 718, "mtime": 1753956723299, "results": "42", "hashOfConfig": "32"}, {"size": 5256, "mtime": 1753947719447, "results": "43", "hashOfConfig": "32"}, {"size": 5798, "mtime": 1753956444931, "results": "44", "hashOfConfig": "32"}, {"size": 7336, "mtime": 1753954465602, "results": "45", "hashOfConfig": "32"}, {"size": 10966, "mtime": 1753954381750, "results": "46", "hashOfConfig": "32"}, {"size": 457, "mtime": 1753947547594, "results": "47", "hashOfConfig": "32"}, {"size": 1744, "mtime": 1753947571236, "results": "48", "hashOfConfig": "32"}, {"size": 595, "mtime": 1753947554463, "results": "49", "hashOfConfig": "32"}, {"size": 578, "mtime": 1753947532393, "results": "50", "hashOfConfig": "32"}, {"size": 451, "mtime": 1753947542309, "results": "51", "hashOfConfig": "32"}, {"size": 6728, "mtime": 1754026271924, "results": "52", "hashOfConfig": "32"}, {"size": 12148, "mtime": 1753948903745, "results": "53", "hashOfConfig": "32"}, {"size": 4872, "mtime": 1754026224118, "results": "54", "hashOfConfig": "32"}, {"size": 8078, "mtime": 1753962966841, "results": "55", "hashOfConfig": "32"}, {"size": 8755, "mtime": 1753950485118, "results": "56", "hashOfConfig": "32"}, {"size": 8798, "mtime": 1753950516686, "results": "57", "hashOfConfig": "32"}, {"size": 803, "mtime": 1753956507978, "results": "58", "hashOfConfig": "32"}, {"size": 839, "mtime": 1753956499684, "results": "59", "hashOfConfig": "32"}, {"size": 815, "mtime": 1753956515024, "results": "60", "hashOfConfig": "32"}, {"size": 826, "mtime": 1753956522918, "results": "61", "hashOfConfig": "32"}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ucl0ra", {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/vlsi-workflow/client/src/index.js", [], [], "/home/<USER>/vlsi-workflow/client/src/App.js", [], [], "/home/<USER>/vlsi-workflow/client/src/contexts/WorkflowContext.js", [], [], "/home/<USER>/vlsi-workflow/client/src/components/Dashboard.js", ["152", "153", "154", "155"], [], "/home/<USER>/vlsi-workflow/client/src/components/WorkflowEditor.js", ["156"], [], "/home/<USER>/vlsi-workflow/client/src/contexts/SocketContext.js", [], [], "/home/<USER>/vlsi-workflow/client/src/components/Toolbar.js", ["157", "158"], [], "/home/<USER>/vlsi-workflow/client/src/services/api.js", [], [], "/home/<USER>/vlsi-workflow/client/src/components/sidebar/Sidebar.js", ["159", "160", "161", "162"], [], "/home/<USER>/vlsi-workflow/client/src/components/edges/index.js", [], [], "/home/<USER>/vlsi-workflow/client/src/components/nodes/index.js", [], [], "/home/<USER>/vlsi-workflow/client/src/components/sidebar/CollaborationPanel.js", [], [], "/home/<USER>/vlsi-workflow/client/src/components/sidebar/NodePalette.js", [], [], "/home/<USER>/vlsi-workflow/client/src/components/sidebar/LayersPanel.js", ["163", "164", "165"], [], "/home/<USER>/vlsi-workflow/client/src/components/sidebar/PropertyPanel.js", [], [], "/home/<USER>/vlsi-workflow/client/src/components/nodes/OutputNode.js", ["166"], [], "/home/<USER>/vlsi-workflow/client/src/components/edges/CustomEdge.js", [], [], "/home/<USER>/vlsi-workflow/client/src/components/nodes/DecisionNode.js", ["167"], [], "/home/<USER>/vlsi-workflow/client/src/components/nodes/ProcessNode.js", ["168", "169", "170", "171"], [], "/home/<USER>/vlsi-workflow/client/src/components/nodes/InputNode.js", ["172"], [], "/home/<USER>/vlsi-workflow/client/src/components/nodes/CustomNode.js", ["173"], [], "/home/<USER>/vlsi-workflow/client/src/components/ConfigModal.js", [], [], "/home/<USER>/vlsi-workflow/client/src/components/ContextMenu.js", [], [], "/home/<USER>/vlsi-workflow/client/src/utils/workflowBranching.js", [], [], "/home/<USER>/vlsi-workflow/client/src/contexts/ThemeContext.js", [], [], "/home/<USER>/vlsi-workflow/client/src/components/ThemeSwitcher.js", [], [], "/home/<USER>/vlsi-workflow/client/src/components/nodes/CTSNode.js", [], [], "/home/<USER>/vlsi-workflow/client/src/components/nodes/FloorplanNode.js", [], [], "/home/<USER>/vlsi-workflow/client/src/components/nodes/PlaceNode.js", [], [], "/home/<USER>/vlsi-workflow/client/src/components/nodes/RouteNode.js", [], [], {"ruleId": "174", "severity": 1, "message": "175", "line": 6, "column": 3, "nodeType": "176", "messageId": "177", "endLine": 6, "endColumn": 9}, {"ruleId": "174", "severity": 1, "message": "178", "line": 12, "column": 3, "nodeType": "176", "messageId": "177", "endLine": 12, "endColumn": 7}, {"ruleId": "174", "severity": 1, "message": "179", "line": 15, "column": 3, "nodeType": "176", "messageId": "177", "endLine": 15, "endColumn": 8}, {"ruleId": "174", "severity": 1, "message": "180", "line": 26, "column": 11, "nodeType": "176", "messageId": "177", "endLine": 26, "endColumn": 26}, {"ruleId": "181", "severity": 1, "message": "182", "line": 93, "column": 6, "nodeType": "183", "endLine": 93, "endColumn": 10, "suggestions": "184"}, {"ruleId": "174", "severity": 1, "message": "185", "line": 10, "column": 3, "nodeType": "176", "messageId": "177", "endLine": 10, "endColumn": 11}, {"ruleId": "174", "severity": 1, "message": "186", "line": 33, "column": 5, "nodeType": "176", "messageId": "177", "endLine": 33, "endColumn": 16}, {"ruleId": "174", "severity": 1, "message": "187", "line": 2, "column": 10, "nodeType": "176", "messageId": "177", "endLine": 2, "endColumn": 15}, {"ruleId": "174", "severity": 1, "message": "188", "line": 2, "column": 17, "nodeType": "176", "messageId": "177", "endLine": 2, "endColumn": 27}, {"ruleId": "174", "severity": 1, "message": "189", "line": 2, "column": 29, "nodeType": "176", "messageId": "177", "endLine": 2, "endColumn": 46}, {"ruleId": "174", "severity": 1, "message": "180", "line": 26, "column": 11, "nodeType": "176", "messageId": "177", "endLine": 26, "endColumn": 26}, {"ruleId": "174", "severity": 1, "message": "190", "line": 16, "column": 18, "nodeType": "176", "messageId": "177", "endLine": 16, "endColumn": 23}, {"ruleId": "174", "severity": 1, "message": "191", "line": 18, "column": 9, "nodeType": "176", "messageId": "177", "endLine": 18, "endColumn": 14}, {"ruleId": "174", "severity": 1, "message": "192", "line": 68, "column": 9, "nodeType": "176", "messageId": "177", "endLine": 68, "endColumn": 24}, {"ruleId": "174", "severity": 1, "message": "193", "line": 2, "column": 22, "nodeType": "176", "messageId": "177", "endLine": 2, "endColumn": 30}, {"ruleId": "174", "severity": 1, "message": "194", "line": 2, "column": 21, "nodeType": "176", "messageId": "177", "endLine": 2, "endColumn": 28}, {"ruleId": "174", "severity": 1, "message": "195", "line": 2, "column": 10, "nodeType": "176", "messageId": "177", "endLine": 2, "endColumn": 16}, {"ruleId": "174", "severity": 1, "message": "196", "line": 2, "column": 18, "nodeType": "176", "messageId": "177", "endLine": 2, "endColumn": 26}, {"ruleId": "174", "severity": 1, "message": "197", "line": 3, "column": 15, "nodeType": "176", "messageId": "177", "endLine": 3, "endColumn": 18}, {"ruleId": "174", "severity": 1, "message": "198", "line": 3, "column": 20, "nodeType": "176", "messageId": "177", "endLine": 3, "endColumn": 28}, {"ruleId": "174", "severity": 1, "message": "199", "line": 2, "column": 21, "nodeType": "176", "messageId": "177", "endLine": 2, "endColumn": 27}, {"ruleId": "174", "severity": 1, "message": "200", "line": 39, "column": 9, "nodeType": "176", "messageId": "177", "endLine": 39, "endColumn": 23}, "no-unused-vars", "'Filter' is defined but never used.", "Identifier", "unusedVar", "'Play' is defined but never used.", "'Edit3' is defined but never used.", "'getCurrentTheme' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'createNewWorkflow', 'joinWorkflow', 'leaveWorkflow', and 'loadWorkflow'. Either include them or remove the dependency array.", "ArrayExpression", ["201"], "'RotateCw' is defined but never used.", "'getViewport' is assigned a value but never used.", "'Panel' is defined but never used.", "'PanelGroup' is defined but never used.", "'PanelResizeHandle' is defined but never used.", "'edges' is assigned a value but never used.", "'theme' is assigned a value but never used.", "'updateLayerName' is assigned a value but never used.", "'Download' is defined but never used.", "'Diamond' is defined but never used.", "'Handle' is defined but never used.", "'Position' is defined but never used.", "'Zap' is defined but never used.", "'Settings' is defined but never used.", "'Upload' is defined but never used.", "'getStatusColor' is assigned a value but never used.", {"desc": "202", "fix": "203"}, "Update the dependencies array to be: [createNewWorkflow, id, joinWorkflow, leaveWorkflow, loadWorkflow]", {"range": "204", "text": "205"}, [2240, 2244], "[createNewWorkflow, id, joinWorkflow, leaveWorkflow, loadWorkflow]"]