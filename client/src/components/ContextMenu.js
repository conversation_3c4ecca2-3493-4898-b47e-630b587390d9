import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import {
  Settings,
  Copy,
  Trash2,
  GitBranch
} from 'lucide-react';

const ContextMenu = ({ 
  isVisible, 
  position, 
  onClose, 
  selectedNode, 
  onEditConfig, 
  onNewRun, 
  onDuplicate, 
  onDelete 
}) => {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    console.log('🎯 ContextMenu visibility changed:', isVisible, 'selectedNode:', selectedNode);
    setIsOpen(isVisible);
  }, [isVisible, selectedNode]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isOpen) {
        onClose();
      }
    };

    const handleEscape = (event) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  if (!isOpen || !selectedNode) {
    console.log('🎯 ContextMenu not rendering:', { isOpen, hasSelectedNode: !!selectedNode });
    return null;
  }
  
  console.log('🎯 ContextMenu rendering at position:', position);

  const menuItems = [
    {
      id: 'edit-config',
      label: 'Edit Config',
      icon: Settings,
      onClick: () => {
        console.log('🎯 Edit Config clicked for node:', selectedNode);
        onEditConfig(selectedNode);
        onClose();
      },
      color: 'text-blue-600',
      description: 'Configure node parameters'
    },
    {
      id: 'new-run',
      label: 'New Run',
      icon: GitBranch,
      onClick: () => {
        console.log('🎯 New Run clicked for node:', selectedNode);
        onNewRun(selectedNode);
        onClose();
      },
      color: 'text-green-600',
      description: 'Start new branch from this node'
    },
    {
      id: 'separator-1',
      type: 'separator'
    },
    {
      id: 'duplicate',
      label: 'Duplicate',
      icon: Copy,
      onClick: () => {
        onDuplicate(selectedNode);
        onClose();
      },
      color: 'text-gray-600',
      description: 'Create a copy of this node'
    },
    {
      id: 'delete',
      label: 'Delete',
      icon: Trash2,
      onClick: () => {
        onDelete(selectedNode);
        onClose();
      },
      color: 'text-red-600',
      description: 'Remove this node'
    }
  ];

  const contextMenuStyle = {
    position: 'fixed',
    left: `${position.x}px`,
    top: `${position.y}px`,
    zIndex: 9999,
    pointerEvents: 'auto',
  };

  return createPortal(
    <div
      className="context-menu bg-white rounded-lg shadow-xl border border-gray-200 py-2 min-w-48"
      style={contextMenuStyle}
      onClick={(e) => e.stopPropagation()}
    >
      {/* Header */}
      <div className="px-3 py-2 border-b border-gray-100">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 rounded-full bg-blue-500"></div>
          <span className="text-sm font-medium text-gray-900 truncate">
            {selectedNode.data?.label || selectedNode.id}
          </span>
        </div>
        <div className="text-xs text-gray-500 mt-1">
          {selectedNode.type} node
        </div>
      </div>

      {/* Menu Items */}
      <div className="py-1">
        {menuItems.map((item) => {
          if (item.type === 'separator') {
            return (
              <div key={item.id} className="border-t border-gray-100 my-1"></div>
            );
          }

          const Icon = item.icon;
          return (
            <button
              key={item.id}
              onClick={item.onClick}
              className="w-full flex items-center px-3 py-2 text-sm hover:bg-gray-50 transition-colors group"
            >
              <Icon className={`w-4 h-4 mr-3 ${item.color} group-hover:scale-110 transition-transform`} />
              <div className="flex-1 text-left">
                <div className={`font-medium ${item.color}`}>
                  {item.label}
                </div>
                <div className="text-xs text-gray-500">
                  {item.description}
                </div>
              </div>
              {item.id === 'new-run' && (
                <div className="ml-2 px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">
                  Branch
                </div>
              )}
            </button>
          );
        })}
      </div>

      {/* Footer */}
      <div className="px-3 py-2 border-t border-gray-100 bg-gray-50 rounded-b-lg">
        <div className="text-xs text-gray-500">
          Right-click for more options
        </div>
      </div>
    </div>,
    document.body
  );
};

export default ContextMenu;