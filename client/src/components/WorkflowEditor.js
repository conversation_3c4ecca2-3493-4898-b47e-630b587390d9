import React, { useState, useCallback, useRef, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import ReactFlow, {
  Background,
  Controls,
  MiniMap,
  useReactFlow,
  ReactFlowProvider,
} from 'reactflow';
import 'reactflow/dist/style.css';
import toast from 'react-hot-toast';

import { nodeTypes } from './nodes';
import { edgeTypes } from './edges';
import Sidebar from './sidebar/Sidebar';
import Toolbar from './Toolbar';
import ContextMenu from './ContextMenu';
import { useTheme } from '../contexts/ThemeContext';
import ConfigModal from './ConfigModal';
import { useWorkflow } from '../contexts/WorkflowContext';
import { useSocket } from '../contexts/SocketContext';
import { workflowAPI } from '../services/api';
import { 
  createNewBranch, 
  duplicateNode, 
  validateBranchCreation 
} from '../utils/workflowBranching';

const WorkflowEditorContent = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const reactFlowWrapper = useRef(null);
  const { project } = useReactFlow();
  const { getCurrentTheme } = useTheme();
  
  const {
    nodes,
    edges,
    onNodesChange,
    onEdgesChange,
    onConnect,
    setWorkflow,
    addNode,
    addEdge,
    updateNode,
    updateEdge,
    setLoading,
    setError,
    isLoading,
  } = useWorkflow();

  const { 
    joinWorkflow, 
    leaveWorkflow, 
    emitNodeUpdate, 
    emitEdgeUpdate,
    socket 
  } = useSocket();

  const [selectedNodes, setSelectedNodes] = useState([]);
  const [selectedEdges, setSelectedEdges] = useState([]);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [reactFlowInstance, setReactFlowInstance] = useState(null);
  
  // Context menu state
  const [contextMenu, setContextMenu] = useState({
    isVisible: false,
    position: { x: 0, y: 0 },
    node: null
  });
  
  // Config modal state
  const [configModal, setConfigModal] = useState({
    isOpen: false,
    node: null
  });

  // Load workflow on mount
  useEffect(() => {
    if (id) {
      loadWorkflow(id);
      joinWorkflow(id);
    } else {
      // Create new workflow
      createNewWorkflow();
    }

    return () => {
      if (id) {
        leaveWorkflow(id);
      }
    };
  }, [id]);

  // Socket event listeners
  useEffect(() => {
    if (!socket) return;

    const handleNodeUpdate = (data) => {
      updateNode(data.nodeId, data.updates);
    };

    const handleEdgeUpdate = (data) => {
      updateEdge(data.edgeId, data.updates);
    };

    const handleWorkflowUpdated = (workflow) => {
      setWorkflow(workflow);
      toast.success('Workflow updated by collaborator');
    };

    socket.on('nodeUpdate', handleNodeUpdate);
    socket.on('edgeUpdate', handleEdgeUpdate);
    socket.on('workflowUpdated', handleWorkflowUpdated);

    return () => {
      socket.off('nodeUpdate', handleNodeUpdate);
      socket.off('edgeUpdate', handleEdgeUpdate);
      socket.off('workflowUpdated', handleWorkflowUpdated);
    };
  }, [socket, updateNode, updateEdge, setWorkflow]);

  const loadWorkflow = async (workflowId) => {
    try {
      setLoading(true);
      const workflow = await workflowAPI.getWorkflow(workflowId);
      console.log('🔄 Loaded workflow:', workflow);
      console.log('🔄 Workflow nodes:', workflow.nodes);
      setWorkflow(workflow);
      toast.success('Workflow loaded successfully');
    } catch (error) {
      console.error('Error loading workflow:', error);
      setError('Failed to load workflow');
      toast.error('Failed to load workflow');
    }
  };

  const createNewWorkflow = () => {
    setWorkflow({
      id: null,
      name: 'Untitled Workflow',
      description: '',
      nodes: [],
      edges: [],
      viewport: { x: 0, y: 0, zoom: 1 },
    });
  };

  const saveWorkflow = async () => {
    try {
      const workflowData = {
        name: 'My Workflow', // This should come from a form or state
        description: 'Workflow description',
        nodes,
        edges,
        viewport: reactFlowInstance?.getViewport() || { x: 0, y: 0, zoom: 1 },
      };

      let savedWorkflow;
      if (id) {
        savedWorkflow = await workflowAPI.updateWorkflow(id, workflowData);
        toast.success('Workflow saved successfully');
      } else {
        savedWorkflow = await workflowAPI.createWorkflow(workflowData);
        navigate(`/workflow/${savedWorkflow.id}`, { replace: true });
        toast.success('Workflow created successfully');
      }
      
      setWorkflow(savedWorkflow);
    } catch (error) {
      console.error('Error saving workflow:', error);
      toast.error('Failed to save workflow');
    }
  };

  const onDragOver = useCallback((event) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  const onDrop = useCallback(
    (event) => {
      event.preventDefault();

      const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect();
      const nodeData = JSON.parse(event.dataTransfer.getData('application/reactflow'));

      if (typeof nodeData === 'undefined' || !nodeData) {
        return;
      }

      const position = project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      });

      const newNode = {
        id: `${nodeData.type}_${Date.now()}`,
        type: nodeData.type,
        position,
        data: {
          ...nodeData,
          status: 'pending',
          progress: 0,
        },
      };

      addNode(newNode);
      
      if (id) {
        emitNodeUpdate(id, { type: 'add', node: newNode });
      }
    },
    [project, addNode, id, emitNodeUpdate]
  );

  const onSelectionChange = useCallback(({ nodes, edges }) => {
    setSelectedNodes(nodes);
    setSelectedEdges(edges);
  }, []);

  const handleNodeUpdate = useCallback((nodeId, updates) => {
    updateNode(nodeId, updates);
    
    if (id) {
      emitNodeUpdate(id, { type: 'update', nodeId, updates });
    }
  }, [updateNode, id, emitNodeUpdate]);

  const handleEdgeUpdate = useCallback((edgeId, updates) => {
    updateEdge(edgeId, updates);
    
    if (id) {
      emitEdgeUpdate(id, { type: 'update', edgeId, updates });
    }
  }, [updateEdge, id, emitEdgeUpdate]);

  const onConnectHandler = useCallback((params) => {
    const newEdge = {
      ...params,
      id: `edge_${Date.now()}`,
      type: 'custom',
      data: { label: '', color: '#64748b' },
    };
    
    onConnect(newEdge);
    
    if (id) {
      emitEdgeUpdate(id, { type: 'add', edge: newEdge });
    }
  }, [onConnect, id, emitEdgeUpdate]);

  // Context menu handlers
  const onNodeContextMenu = useCallback((event, node) => {
    console.log('🖱️ Right-click on node:', node);
    console.log('🖱️ Event details:', { clientX: event.clientX, clientY: event.clientY });
    event.preventDefault();
    event.stopPropagation();

    if (!node) {
      console.log('❌ Node is null/undefined, cannot show context menu');
      return;
    }

    const menuState = {
      isVisible: true,
      position: { x: event.clientX, y: event.clientY },
      node
    };
    console.log('🖱️ Setting context menu state:', menuState);
    setContextMenu(menuState);
  }, []);

  const closeContextMenu = useCallback(() => {
    console.log('🖱️ Closing context menu');
    setContextMenu({
      isVisible: false,
      position: { x: 0, y: 0 },
      node: null
    });
  }, []);

  const handleEditConfig = useCallback((node) => {
    setConfigModal({
      isOpen: true,
      node
    });
  }, []);

  const handleNewRun = useCallback((node) => {
    console.log('🚀 handleNewRun called with node:', node);
    console.log('🚀 Current nodes count:', nodes.length);
    console.log('🚀 Current edges count:', edges.length);
    
    const validation = validateBranchCreation(node, nodes, edges);
    console.log('🚀 Validation result:', validation);

    if (!validation.isValid) {
      console.log('❌ Validation failed:', validation.errors);
      validation.errors.forEach(error => toast.error(error));
      return;
    }

    const { newNodes, newEdges } = createNewBranch(nodes, edges, node.id);
    console.log('🚀 Branch creation result:', { newNodesCount: newNodes.length, newEdgesCount: newEdges.length });

    if (newNodes.length === 0) {
      console.log('❌ No new nodes created');
      toast.error('Could not create branch from this node');
      return;
    }

    console.log('✅ Adding nodes and edges to workflow');
    // Add new nodes and edges to the workflow
    newNodes.forEach(newNode => {
      console.log('➕ Adding node:', newNode.id, newNode.type);
      addNode(newNode);
    });
    newEdges.forEach(newEdge => {
      console.log('➕ Adding edge:', newEdge.id);
      addEdge(newEdge);
    });

    toast.success(`New branch created from ${node.data?.label || node.id}`);

    // Emit updates for real-time collaboration
    if (id) {
      newNodes.forEach(newNode => {
        emitNodeUpdate(id, { type: 'add', node: newNode });
      });
      newEdges.forEach(newEdge => {
        emitEdgeUpdate(id, { type: 'add', edge: newEdge });
      });
    }
  }, [nodes, edges, addNode, addEdge, id, emitNodeUpdate, emitEdgeUpdate]);

  const handleDuplicateNode = useCallback((node) => {
    const duplicatedNode = duplicateNode(node, nodes);
    addNode(duplicatedNode);
    
    toast.success(`Node duplicated: ${duplicatedNode.data?.label}`);
    
    if (id) {
      emitNodeUpdate(id, { type: 'add', node: duplicatedNode });
    }
  }, [nodes, addNode, id, emitNodeUpdate]);

  const handleDeleteNode = useCallback((node) => {
    if (window.confirm(`Are you sure you want to delete "${node.data?.label || node.id}"?`)) {
      // Remove connected edges first
      const connectedEdges = edges.filter(edge => 
        edge.source === node.id || edge.target === node.id
      );
      
      connectedEdges.forEach(edge => {
        onEdgesChange([{ type: 'remove', id: edge.id }]);
      });
      
      // Remove the node
      onNodesChange([{ type: 'remove', id: node.id }]);
      
      toast.success(`Node deleted: ${node.data?.label || node.id}`);
      
      if (id) {
        emitNodeUpdate(id, { type: 'delete', nodeId: node.id });
      }
    }
  }, [edges, onEdgesChange, onNodesChange, id, emitNodeUpdate]);

  const handleSaveConfig = useCallback((nodeId, config) => {
    handleNodeUpdate(nodeId, { data: config });
    toast.success('Configuration saved successfully');
  }, [handleNodeUpdate]);

  const closeConfigModal = useCallback(() => {
    setConfigModal({
      isOpen: false,
      node: null
    });
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  const theme = getCurrentTheme();
  
  return (
    <div className="flex h-screen" style={{ backgroundColor: 'var(--color-primary)' }}>
      <Sidebar
        selectedNode={selectedNodes[0]}
        selectedEdge={selectedEdges[0]}
        onUpdateNode={handleNodeUpdate}
        onUpdateEdge={handleEdgeUpdate}
        isCollapsed={sidebarCollapsed}
        onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
      />
      
      <div className="flex-1 flex flex-col">
        <Toolbar onSave={saveWorkflow} />
        
        <div className="flex-1" ref={reactFlowWrapper}>
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnectHandler}
            onInit={setReactFlowInstance}
            onDrop={onDrop}
            onDragOver={onDragOver}
            onSelectionChange={onSelectionChange}
            onNodeClick={(event, node) => {
              console.log('🖱️ Node clicked:', node);
            }}
            onNodeContextMenu={onNodeContextMenu}
            onPaneContextMenu={(event) => {
              console.log('🖱️ Right-click on pane (background)');
              event.preventDefault();
            }}
            nodeTypes={nodeTypes}
            edgeTypes={edgeTypes}
            fitView
            attributionPosition="bottom-left"
            style={{ backgroundColor: theme.colors.secondary }}
          >
            <Background 
              color={theme.colors.border}
              gap={20} 
              size={1}
              variant="dots"
            />
            <Controls 
              className="themed-card rounded-lg"
              style={{ 
                backgroundColor: `${theme.colors.cardBg}dd`,
                backdropFilter: 'blur(8px)'
              }}
            />
            <MiniMap 
              className="themed-card rounded-lg"
              style={{ 
                backgroundColor: `${theme.colors.cardBg}dd`,
                backdropFilter: 'blur(8px)'
              }}
              nodeColor={(node) => {
                switch (node.type) {
                  case 'input': return theme.colors.success;
                  case 'output': return theme.colors.brand;
                  case 'process': return theme.colors.info;
                  case 'decision': return theme.colors.warning;
                  default: return theme.colors.textTertiary;
                }
              }}
              maskColor={`${theme.colors.primary}1a`}
            />
          </ReactFlow>
        </div>
      </div>

      {/* Context Menu */}
      <ContextMenu
        isVisible={contextMenu.isVisible}
        position={contextMenu.position}
        selectedNode={contextMenu.node}
        onClose={closeContextMenu}
        onEditConfig={handleEditConfig}
        onNewRun={handleNewRun}
        onDuplicate={handleDuplicateNode}
        onDelete={handleDeleteNode}
      />

      {/* Configuration Modal */}
      <ConfigModal
        isOpen={configModal.isOpen}
        node={configModal.node}
        onClose={closeConfigModal}
        onSave={handleSaveConfig}
      />
    </div>
  );
};

const WorkflowEditor = () => {
  return (
    <ReactFlowProvider>
      <WorkflowEditorContent />
    </ReactFlowProvider>
  );
};

export default WorkflowEditor;