import React, { memo, useState } from 'react';
import { <PERSON><PERSON>, Position } from 'reactflow';
import { useTheme } from '../../contexts/ThemeContext';
import { 
  Settings, 
  Play, 
  Pause, 
  CheckCircle, 
  XCircle, 
  Clock,
  AlertTriangle,
  Info
} from 'lucide-react';

const CustomNode = ({ data, selected }) => {
  const [isHovered, setIsHovered] = useState(false);
  const { getCurrentTheme } = useTheme();
  const theme = getCurrentTheme();

  const getStatusIcon = (status) => {
    switch (status) {
      case 'running':
        return <Play className="w-4 h-4" style={{ color: theme.colors.info }} />;
      case 'paused':
        return <Pause className="w-4 h-4" style={{ color: theme.colors.warning }} />;
      case 'completed':
        return <CheckCircle className="w-4 h-4" style={{ color: theme.colors.success }} />;
      case 'failed':
        return <XCircle className="w-4 h-4" style={{ color: theme.colors.error }} />;
      case 'pending':
        return <Clock className="w-4 h-4" style={{ color: theme.colors.textTertiary }} />;
      case 'warning':
        return <AlertTriangle className="w-4 h-4" style={{ color: theme.colors.warning }} />;
      default:
        return <Info className="w-4 h-4" style={{ color: theme.colors.textTertiary }} />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'running':
        return 'border-blue-500 bg-blue-50';
      case 'paused':
        return 'border-yellow-500 bg-yellow-50';
      case 'completed':
        return 'border-green-500 bg-green-50';
      case 'failed':
        return 'border-red-500 bg-red-50';
      case 'pending':
        return 'border-gray-300 bg-gray-50';
      case 'warning':
        return 'border-orange-500 bg-orange-50';
      default:
        return 'border-gray-300 bg-white';
    }
  };

  return (
    <div
      className={`workflow-node themed-card rounded-lg p-4 cursor-pointer transition-all duration-200 min-w-[200px] max-w-[300px] ${selected ? 'glow-effect' : ''} ${data.status === 'completed' ? 'status-success' : ''} ${data.status === 'failed' ? 'status-error' : ''}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={() => {
        console.log('🎯 CustomNode clicked:', data);
      }}
      style={{
        backgroundColor: theme.colors.nodeBackground,
        borderColor: selected ? theme.colors.brand : theme.colors.nodeBorder,
        borderWidth: '2px',
        borderStyle: 'solid'
      }}
    >
      {/* Input Handles */}
      {data.inputs?.map((input, index) => (
        <Handle
          key={`input-${index}`}
          type="target"
          position={Position.Left}
          id={input.id}
          style={{
            top: `${20 + (index * 25)}px`,
            background: input.connected ? theme.colors.success : theme.colors.textTertiary,
            borderColor: theme.colors.nodeBackground,
          }}
          className="w-3 h-3 border-2"
        />
      ))}

      {/* Output Handles */}
      {data.outputs?.map((output, index) => (
        <Handle
          key={`output-${index}`}
          type="source"
          position={Position.Right}
          id={output.id}
          style={{
            top: `${20 + (index * 25)}px`,
            background: output.connected ? theme.colors.success : theme.colors.textTertiary,
            borderColor: theme.colors.nodeBackground,
          }}
          className="w-3 h-3 border-2"
        />
      ))}

      {/* Node Header */}
      <div className="node-header flex items-center justify-between mb-3 pb-2" style={{ borderBottom: `1px solid ${theme.colors.border}` }}>
        <div className="flex items-center space-x-2">
          {data.icon && (
            <div className="w-6 h-6 flex items-center justify-center">
              {data.icon}
            </div>
          )}
          <h3 className="node-title font-semibold text-sm" style={{ color: theme.colors.textPrimary }}>
            {data.label || 'Untitled Node'}
          </h3>
        </div>
        <div className="flex items-center space-x-1">
          {getStatusIcon(data.status)}
          {isHovered && (
            <button className="p-1 themed-button-secondary rounded transition-colors">
              <Settings className="w-4 h-4" style={{ color: theme.colors.textTertiary }} />
            </button>
          )}
        </div>
      </div>

      {/* Node Content */}
      <div className="node-content">
        {data.description && (
          <p className="text-xs mb-2" style={{ color: theme.colors.textSecondary }}>
            {data.description}
          </p>
        )}
        
        {data.parameters && Object.keys(data.parameters).length > 0 && (
          <div className="space-y-1">
            {Object.entries(data.parameters).slice(0, 3).map(([key, value]) => (
              <div key={key} className="flex justify-between text-xs">
                <span className="text-gray-500 truncate">{key}:</span>
                <span className="text-gray-700 font-mono truncate ml-2">
                  {String(value).length > 15 ? `${String(value).slice(0, 15)}...` : String(value)}
                </span>
              </div>
            ))}
            {Object.keys(data.parameters).length > 3 && (
              <div className="text-xs text-gray-400 italic">
                +{Object.keys(data.parameters).length - 3} more...
              </div>
            )}
          </div>
        )}

        {data.progress !== undefined && (
          <div className="mt-2">
            <div className="flex justify-between text-xs text-gray-600 mb-1">
              <span>Progress</span>
              <span>{Math.round(data.progress)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${data.progress}%` }}
              />
            </div>
          </div>
        )}

        {data.executionTime && (
          <div className="mt-2 text-xs text-gray-500">
            <Clock className="w-3 h-3 inline mr-1" />
            {data.executionTime}
          </div>
        )}
      </div>

      {/* Node Footer */}
      {(data.inputs?.length > 0 || data.outputs?.length > 0) && (
        <div className="mt-3 pt-2 border-t border-gray-200 flex justify-between text-xs text-gray-500">
          {data.inputs?.length > 0 && (
            <span>{data.inputs.length} input{data.inputs.length !== 1 ? 's' : ''}</span>
          )}
          {data.outputs?.length > 0 && (
            <span>{data.outputs.length} output{data.outputs.length !== 1 ? 's' : ''}</span>
          )}
        </div>
      )}
    </div>
  );
};

export default memo(CustomNode);