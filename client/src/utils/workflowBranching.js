// Utility functions for workflow branching and node management

export const createNewBranch = (nodes, edges, startNodeId) => {
  console.log('🔧 createNewBranch called with:', { startNodeId, nodesCount: nodes.length, edgesCount: edges.length });
  
  // Define the standard VLSI flow sequence
  const vlsiFlowSequence = [
    { type: 'floorplan', label: 'Floorplan' },
    { type: 'place', label: 'Place' },
    { type: 'cts', label: 'CTS' },
    { type: 'route', label: 'Route' }
  ];

  // Find the starting node
  const startNode = nodes.find(node => node.id === startNodeId);
  console.log('🔧 Found startNode:', startNode);
  if (!startNode) {
    console.log('❌ No start node found');
    return { newNodes: [], newEdges: [] };
  }

  // Determine the starting point in the sequence
  let startIndex = vlsiFlowSequence.findIndex(step => {
    const nodeType = startNode.type?.toLowerCase();
    const nodeLabel = startNode.data?.label?.toLowerCase();
    const nodeId = startNode.id?.toLowerCase();

    return nodeType === step.type ||
           nodeLabel === step.type ||
           nodeLabel === step.label.toLowerCase() ||
           nodeId.includes(step.type);
  });

  console.log('🔧 Node matching:', {
    nodeType: startNode.type,
    nodeLabel: startNode.data?.label,
    nodeId: startNode.id,
    startIndex
  });

  // If not found in standard sequence, treat as custom node
  if (startIndex === -1) {
    console.log('⚠️ Node not found in VLSI sequence, starting from floorplan');
    startIndex = 0; // Start from floorplan
  }

  // Generate branch suffix
  const branchSuffix = `_branch_${Date.now()}`;
  const newNodes = [];
  const newEdges = [];

  // Create new nodes for the branch starting from the NEXT stage after the selected node
  // This creates a branch that continues from the clicked node with subsequent stages only
  const branchSteps = vlsiFlowSequence.slice(startIndex + 1); // Start from NEXT stage after clicked node
  console.log('🔧 Branch steps to create:', branchSteps);
  let previousNodeId = startNodeId; // Start connecting from the original clicked node

  // Create nodes for all subsequent stages in the branch
  branchSteps.forEach((step, index) => {
    const nodeId = `${step.type}${branchSuffix}`;
    const xOffset = 300; // Horizontal offset for branch
    const yOffset = 100 + (index * 150); // Vertical spacing

    // Calculate position relative to the start node
    const newNode = {
      id: nodeId,
      type: step.type, // Use the actual VLSI node type
      position: {
        x: startNode.position.x + xOffset,
        y: startNode.position.y + yOffset
      },
      data: {
        label: `${step.label} (Branch)`,
        description: `New branch execution of ${step.label}`,
        status: 'pending',
        progress: 0,
        branch: true,
        branchId: branchSuffix,
        parentNode: startNodeId,
        parameters: getDefaultParameters(step.type)
      }
    };

    newNodes.push(newNode);

    // Create edge from previous node
    const edgeId = `edge_${previousNodeId}_to_${nodeId}`;
    const isFirstInBranch = index === 0;

    newEdges.push({
      id: edgeId,
      source: previousNodeId,
      target: nodeId,
      type: 'custom',
      data: {
        label: isFirstInBranch ? 'New Branch' : 'Branch Flow',
        color: isFirstInBranch ? '#10b981' : '#3b82f6',
        dashed: isFirstInBranch
      }
    });

    previousNodeId = nodeId;
  });

  // If no subsequent steps are found (e.g., clicked on Route node - the last stage)
  // Create a duplicate of the same stage as a branch
  if (branchSteps.length === 0) {
    const currentStep = vlsiFlowSequence[startIndex] || { type: startNode.type, label: startNode.data?.label || 'Node' };
    const nodeId = `${currentStep.type}${branchSuffix}`;
    const xOffset = 300;
    const yOffset = 100;

    const newNode = {
      id: nodeId,
      type: currentStep.type,
      position: {
        x: startNode.position.x + xOffset,
        y: startNode.position.y + yOffset
      },
      data: {
        label: `${currentStep.label} (Branch)`,
        description: `New branch execution of ${currentStep.label}`,
        status: 'pending',
        progress: 0,
        branch: true,
        branchId: branchSuffix,
        parentNode: startNodeId,
        parameters: getDefaultParameters(currentStep.type)
      }
    };

    newNodes.push(newNode);

    // Connect to the original start node
    const edgeId = `edge_${startNodeId}_to_${nodeId}`;
    newEdges.push({
      id: edgeId,
      source: startNodeId,
      target: nodeId,
      type: 'custom',
      data: {
        label: 'New Branch',
        color: '#10b981',
        dashed: true
      }
    });
  }

  console.log('🔧 Final result:', { newNodesCount: newNodes.length, newEdgesCount: newEdges.length });
  console.log('🔧 New nodes:', newNodes.map(n => ({ id: n.id, type: n.type, label: n.data.label })));
  
  return { newNodes, newEdges };
};

export const getDefaultParameters = (nodeType) => {
  const defaultParams = {
    floorplan: {
      'aspect_ratio': '1.0',
      'utilization': '70%',
      'power_rings': 'enabled',
      'io_placement': 'auto',
      'core_margin': '10um'
    },
    place: {
      'density': '0.8',
      'timing_driven': 'true',
      'congestion_driven': 'true',
      'effort': 'high',
      'max_displacement': '5um'
    },
    cts: {
      'skew_target': '50ps',
      'buffer_type': 'CLKBUF',
      'max_fanout': '16',
      'insertion_delay': '100ps',
      'balance_mode': 'top_down'
    },
    route: {
      'layers': '9',
      'via_optimization': 'enabled',
      'timing_driven': 'true',
      'crosstalk_prevention': 'enabled',
      'detail_route_effort': 'high'
    }
  };

  return defaultParams[nodeType] || {};
};

export const duplicateNode = (node, nodes) => {
  const duplicateId = `${node.id}_copy_${Date.now()}`;
  const offset = 50;

  return {
    ...node,
    id: duplicateId,
    position: {
      x: node.position.x + offset,
      y: node.position.y + offset
    },
    data: {
      ...node.data,
      label: `${node.data?.label || node.id} (Copy)`,
      status: 'pending',
      progress: 0
    }
  };
};

export const getNodesByType = (nodes, nodeType) => {
  return nodes.filter(node => 
    node.type === nodeType || 
    node.data?.label?.toLowerCase().includes(nodeType) ||
    node.id.toLowerCase().includes(nodeType)
  );
};

export const findDownstreamNodes = (nodeId, edges, nodes) => {
  const downstreamNodeIds = new Set();
  const visited = new Set();

  const traverse = (currentNodeId) => {
    if (visited.has(currentNodeId)) return;
    visited.add(currentNodeId);

    const outgoingEdges = edges.filter(edge => edge.source === currentNodeId);
    outgoingEdges.forEach(edge => {
      downstreamNodeIds.add(edge.target);
      traverse(edge.target);
    });
  };

  traverse(nodeId);
  return nodes.filter(node => downstreamNodeIds.has(node.id));
};

export const validateBranchCreation = (startNode, nodes, edges) => {
  const errors = [];

  if (!startNode) {
    errors.push('No start node specified');
    return { isValid: false, errors };
  }

  // Check if node is already running
  if (startNode.data?.status === 'running') {
    errors.push('Cannot create branch from running node');
  }

  // Check if there are already too many branches
  const existingBranches = nodes.filter(node =>
    node.data?.branch && node.data?.parentNode === startNode.id
  );

  if (existingBranches.length >= 3) {
    errors.push('Maximum number of branches (3) already exists for this node');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

export const getBranchInfo = (node) => {
  if (!node.data?.branch) {
    return null;
  }

  return {
    branchId: node.data.branchId,
    parentNode: node.data.parentNode,
    isBranch: true
  };
};

export const getNodeExecutionOrder = (nodeType) => {
  const executionOrder = {
    'floorplan': 1,
    'place': 2,
    'cts': 3,
    'route': 4
  };

  return executionOrder[nodeType] || 0;
};