import { createNewBranch } from '../workflowBranching';

describe('Workflow Branching', () => {
  const mockNodes = [
    {
      id: 'floorplan-main',
      type: 'floorplan',
      position: { x: 200, y: 200 },
      data: { label: 'Floorplan', status: 'completed' }
    },
    {
      id: 'place-main',
      type: 'place',
      position: { x: 200, y: 350 },
      data: { label: 'Place', status: 'completed' }
    },
    {
      id: 'cts-main',
      type: 'cts',
      position: { x: 200, y: 500 },
      data: { label: 'CTS', status: 'pending' }
    },
    {
      id: 'route-main',
      type: 'route',
      position: { x: 200, y: 650 },
      data: { label: 'Route', status: 'pending' }
    }
  ];

  const mockEdges = [
    { id: 'e1', source: 'floorplan-main', target: 'place-main' },
    { id: 'e2', source: 'place-main', target: 'cts-main' },
    { id: 'e3', source: 'cts-main', target: 'route-main' }
  ];

  test('should create branch from floorplan with all subsequent stages', () => {
    const result = createNewBranch(mockNodes, mockEdges, 'floorplan-main');
    
    expect(result.newNodes).toHaveLength(3); // place, cts, route
    expect(result.newEdges).toHaveLength(3); // floorplan->place, place->cts, cts->route
    
    // Check that first node is place (next after floorplan)
    expect(result.newNodes[0].type).toBe('place');
    expect(result.newNodes[1].type).toBe('cts');
    expect(result.newNodes[2].type).toBe('route');
    
    // Check that first edge connects from original floorplan node
    expect(result.newEdges[0].source).toBe('floorplan-main');
    expect(result.newEdges[0].data.label).toBe('New Branch');
    expect(result.newEdges[0].data.dashed).toBe(true);
  });

  test('should create branch from place with cts and route', () => {
    const result = createNewBranch(mockNodes, mockEdges, 'place-main');
    
    expect(result.newNodes).toHaveLength(2); // cts, route
    expect(result.newEdges).toHaveLength(2); // place->cts, cts->route
    
    // Check that first node is cts (next after place)
    expect(result.newNodes[0].type).toBe('cts');
    expect(result.newNodes[1].type).toBe('route');
    
    // Check that first edge connects from original place node
    expect(result.newEdges[0].source).toBe('place-main');
    expect(result.newEdges[0].data.label).toBe('New Branch');
  });

  test('should create branch from cts with only route', () => {
    const result = createNewBranch(mockNodes, mockEdges, 'cts-main');
    
    expect(result.newNodes).toHaveLength(1); // route
    expect(result.newEdges).toHaveLength(1); // cts->route
    
    // Check that node is route (next after cts)
    expect(result.newNodes[0].type).toBe('route');
    
    // Check that edge connects from original cts node
    expect(result.newEdges[0].source).toBe('cts-main');
    expect(result.newEdges[0].data.label).toBe('New Branch');
  });

  test('should create duplicate route when branching from route (last stage)', () => {
    const result = createNewBranch(mockNodes, mockEdges, 'route-main');
    
    expect(result.newNodes).toHaveLength(1); // duplicate route
    expect(result.newEdges).toHaveLength(1); // route->route
    
    // Check that node is route (duplicate of same stage)
    expect(result.newNodes[0].type).toBe('route');
    expect(result.newNodes[0].data.label).toBe('Route (Branch)');
    
    // Check that edge connects from original route node
    expect(result.newEdges[0].source).toBe('route-main');
    expect(result.newEdges[0].data.label).toBe('New Branch');
  });

  test('should handle invalid node id', () => {
    const result = createNewBranch(mockNodes, mockEdges, 'invalid-node');
    
    expect(result.newNodes).toHaveLength(0);
    expect(result.newEdges).toHaveLength(0);
  });

  test('should position new nodes correctly', () => {
    const result = createNewBranch(mockNodes, mockEdges, 'place-main');
    
    // Check positioning relative to original node
    const originalNode = mockNodes.find(n => n.id === 'place-main');
    const firstNewNode = result.newNodes[0];
    
    expect(firstNewNode.position.x).toBe(originalNode.position.x + 300);
    expect(firstNewNode.position.y).toBe(originalNode.position.y + 100);
  });

  test('should mark new nodes as branch nodes', () => {
    const result = createNewBranch(mockNodes, mockEdges, 'place-main');
    
    result.newNodes.forEach(node => {
      expect(node.data.branch).toBe(true);
      expect(node.data.parentNode).toBe('place-main');
      expect(node.data.branchId).toBeDefined();
      expect(node.data.status).toBe('pending');
      expect(node.data.progress).toBe(0);
    });
  });
});
